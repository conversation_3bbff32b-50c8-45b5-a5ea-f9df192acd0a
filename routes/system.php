<?php

use App\Http\Controllers\System\PlotsController;
use App\Http\Controllers\System\PlotsSoilController;
use App\Http\Controllers\System\StationsController;
use Illuminate\Support\Facades\Route;

Route::group([], function () {
    // StationsController
    Route::post('stations/deactivate-stations-by-contract', [StationsController::class, 'deactivateStationsByContract']);
    Route::post('stations/set-contract-to-stations', [StationsController::class, 'setContractToStations']);

    // PlotsSoilController
    Route::get('plots/grid-points', [PlotsSoilController::class, 'getGridPointsDataBySoprIds']);
    Route::post('plots/make-soil-map', [PlotsSoilController::class, 'makeSoilMap']);
    Route::get('plots/details/{plotUuId}', [PlotsController::class, 'getPlotByUuid']);
    Route::get('plots/details/{plotId}/crops', [PlotsController::class, 'getCrops']);
});
