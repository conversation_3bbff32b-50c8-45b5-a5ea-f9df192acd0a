<?php
/**
 * Created by PhpStor<PERSON>.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 03/10/2018
 * Time: 08:27.
 */

use App\Http\Controllers\APIGS\Common;
use App\Http\Controllers\APIGS\FarmTrackReportsLogController;
use App\Http\Controllers\APIGS\FilesController;
use App\Http\Controllers\APIGS\IntegrationAddressController;
use App\Http\Controllers\APIGS\IntegrationController;
use App\Http\Controllers\APIGS\IrrigationEventsController;
use App\Http\Controllers\APIGS\IrrigationPlatformController;
use App\Http\Controllers\APIGS\IrrigationUnitsController;
use App\Http\Controllers\APIGS\MachineImplementController;
use App\Http\Controllers\APIGS\MachinesController;
use App\Http\Controllers\APIGS\MachineTaskProductsController;
use App\Http\Controllers\APIGS\MachineTasksController;
use App\Http\Controllers\APIGS\MachineUnitsController;
use App\Http\Controllers\APIGS\MapController;
use App\Http\Controllers\APIGS\NotificationsController;
use App\Http\Controllers\APIGS\OrderPlotsController;
use App\Http\Controllers\APIGS\OrdersController;
use App\Http\Controllers\APIGS\OrderSoilVraController;
use App\Http\Controllers\APIGS\OrderVraController;
use App\Http\Controllers\APIGS\Organization;
use App\Http\Controllers\APIGS\OrganizationController;
use App\Http\Controllers\APIGS\PestsDiseasesController;
use App\Http\Controllers\APIGS\PinsController;
use App\Http\Controllers\APIGS\PlotsController;
use App\Http\Controllers\APIGS\PlotsCropsController;
use App\Http\Controllers\APIGS\PlotsNotesController;
use App\Http\Controllers\APIGS\PlotsRecommendationsController;
use App\Http\Controllers\APIGS\PlotsSoilController;
use App\Http\Controllers\APIGS\ProtocolController;
use App\Http\Controllers\APIGS\RecommendationsController;
use App\Http\Controllers\APIGS\RoleController;
use App\Http\Controllers\APIGS\ScheduledReportsController;
use App\Http\Controllers\APIGS\StaticMapController;
use App\Http\Controllers\APIGS\StationsController;
use App\Http\Controllers\APIGS\UserManagementController;
use App\Http\Controllers\APIGS\UsersController;
use App\Http\Controllers\APIGS\WorkOperationController;
use Illuminate\Support\Facades\Route;

// FilesController
Route::get('files', [FilesController::class, 'getIndex']);
Route::post('files/upload', [FilesController::class, 'postUpload']);
Route::get('files/definition', [FilesController::class, 'getDefinition']);
Route::get('files/tmp/plots-boundaries', [FilesController::class, 'getPlotsBoundariesInfo']);
Route::delete('files/tmp/plots-boundaries', [FilesController::class, 'deletePlotsBoundaries']);
Route::post('files/save-definition', [FilesController::class, 'postSaveDefinition']);

// MapController
Route::get('map/select/{longitude}/{latitude}', [MapController::class, 'getSelect']);
Route::get('map/select', [MapController::class, 'getSelect']);
Route::get('map/last-layer', [MapController::class, 'getLastLayer']);
Route::get('map/gs-available-dates', [MapController::class, 'getGsAvailableDates'])->middleware('ability:view_sat_data');
Route::get('map/extent', [MapController::class, 'getExtent']);
Route::post('map/save-work-map', [MapController::class, 'postSaveWorkMap'])->middleware('ability:edit_field_boundaries');

// PinsController
Route::delete('pins/selected-pin', [PinsController::class, 'deleteSelectedPin']);
Route::delete('pins/pin-image', [PinsController::class, 'deletePinImage']);
Route::get('pins/load-pin', [PinsController::class, 'getLoadPin']);
Route::post('pins/edit-pin', [PinsController::class, 'postEditPin']);
Route::post('pins/create-pin', [PinsController::class, 'postCreatePin']);
Route::post('pins/upload-images/{pinId}', [PinsController::class, 'postUploadImages']);
Route::get('pins/for-map', [PinsController::class, 'getForMap']);
Route::get('pins/for-soil-samples', [PinsController::class, 'getForSoilSamples']);
Route::get('pins/index/{id?}', [PinsController::class, 'getIndex']);

// StationsController
Route::get('stations', [StationsController::class, 'getForMap'])->middleware('ability:view_weather_stations');
Route::get('stations/for-map', [StationsController::class, 'getForMap'])->middleware('ability:view_weather_stations');
Route::get('stations/data-history', [StationsController::class, 'getDataHistory'])->middleware('ability:view_weather_data');
Route::get('stations/last-data', [StationsController::class, 'getLastData'])->middleware('ability:view_weather_data');
Route::get('stations/hourly-data-history', [StationsController::class, 'getHourlyDataHistory'])->middleware('ability:view_weather_data');
Route::get('stations/data-history-charts', [StationsController::class, 'getDataHistoryEChartsFormatted'])->middleware('ability:view_weather_data');
Route::get('stations/data', [StationsController::class, 'getData'])->middleware('ability:view_weather_data');
Route::get('stations/report-list', [StationsController::class, 'getStationsReportList'])->middleware('ability:view_weather_data');
Route::get('stations/download-report-list', [StationsController::class, 'downloadReportList'])->middleware('ability:view_weather_data');

// OrdersController
Route::post('orders/{orderId}/duplicate', [OrdersController::class, 'duplicateOrder']);
Route::post('orders/{orderId}/plots/duplicate', [OrdersController::class, 'duplicateOrderByPlots']);
Route::get('orders', [OrdersController::class, 'getIndex']);
Route::post('orders/store', [OrdersController::class, 'postStore']);
Route::get('orders/vra-orders', [OrdersController::class, 'getVraOrders'])->middleware('ability:view_vra_maps');
Route::get('orders/index-adjustment', [OrdersController::class, 'getIndexAdjustment']);

// OrderPlotsController
Route::get('order/{orderId}/plots', [OrderPlotsController::class, 'getPlotsByOrderId']);

Route::group([], function () {
    // OrderVraController
    Route::get('order-vra/orders', [OrderVraController::class, 'getOrders'])->middleware('ability:view_vra_maps');
    Route::post('order-vra/export', [OrderVraController::class, 'getExport'])->middleware('ability:view_vra_maps');
    Route::post('order-vra/create-order', [OrderVraController::class, 'postCreateOrder']);
    Route::post('order-vra/cancel-order', [OrderVraController::class, 'postCancelOrder']);

    // OrderSoilVraController
    Route::get('order-soil-vra/orders', [OrderSoilVraController::class, 'getOrders'])->middleware('ability:view_vra_maps');
    Route::post('order-soil-vra/create-order', [OrderSoilVraController::class, 'postCreateOrder']);
    Route::post('order-soil-vra/export', [OrderSoilVraController::class, 'getExport'])->middleware('ability:view_vra_maps');
});

// PlotsCropsController
Route::get('plots/{plotId}/crops', [PlotsCropsController::class, 'getCrops']);
Route::post('plots/{plotId}/crops/replace', [PlotsCropsController::class, 'postReplace']);
Route::post('plots/{plotId}/crops/save-gdd-settings', [PlotsCropsController::class, 'postSaveGddSettings']);
Route::post('plots/{plotId}/crops', [PlotsCropsController::class, 'postStore']);
Route::post('plots/{plotId}/crops/make-primary/{relId}', [PlotsCropsController::class, 'postMakePrimary']);
Route::put('plots/{plotId}/crops/{relId}', [PlotsCropsController::class, 'putUpdate']);
Route::delete('plots/{plotId}/crops/{relId}', [PlotsCropsController::class, 'destroy']);
Route::post('plots/crops/set-irrigated', [PlotsCropsController::class, 'setIrrigated']);

// PlotsSoilController
Route::get('plots/{plotId}/soil', [PlotsSoilController::class, 'getFiles'])->middleware('ability:view_soil_maps');
Route::get('plots/{plotId}/soil/{fileId}/samples', [PlotsSoilController::class, 'getSamples'])->middleware('ability:view_soil_maps');
Route::get('plots/soil/sampling-years', [PlotsSoilController::class, 'getPlotsSamplingYears']);

// PlotsRecommendationsController
Route::get('plots/{plotId}/recommendations', [PlotsRecommendationsController::class, 'getIndex']);

// PlotsController
Route::get('plots', [PlotsController::class, 'getPlots']);
Route::get('plots/{plotId}/pins', [PlotsController::class, 'getPins']);
Route::get('plots/{plotId}/history', [PlotsController::class, 'getHistory']);
Route::get('plots/{plotId}/element-dynamic-classes', [PlotsController::class, 'getElementDynamicClasses']);
Route::get('plots/{plot}/element-dynamic-classes-vectorized', [PlotsController::class, 'getElementDynamicClassesVectorized']);
Route::get('plots/element-default-classes', [PlotsController::class, 'getElementsDefaultClasses']);
Route::get('plots/{plotId}/mean-index', [PlotsController::class, 'getMeanIndex'])->middleware('ability:view_sat_data');
Route::get('plots/{plotId}/soil-sampling-dates', [PlotsController::class, 'getSoilSamplingDates']);
Route::get('plots/crop-analysis/echarts', [PlotsController::class, 'getCropAnalysisECharts']);
Route::get('plots/images-dates', [PlotsController::class, 'getImagesDates'])->middleware('ability:view_sat_data');
Route::get('plots/index-analysis/gcharts', [PlotsController::class, 'getIndexAnalysisGCharts'])->middleware('ability:view_sat_data');
Route::get('plots/index-analysis/echarts', [PlotsController::class, 'getIndexAnalysisECharts'])->middleware('ability:view_sat_data');
Route::get('plots/crop-development/echarts', [PlotsController::class, 'getCropDevelopmentECharts']);
Route::get('plots/analysis/{type}/echarts', [PlotsController::class, 'getIndexAnalysisECharts'])->middleware('ability:view_sat_data');
Route::get('plots/index/{gid}', [PlotsController::class, 'getIndex'])->middleware('ability:view_sat_data');
Route::get('plots/detailed/{gid?}', [PlotsController::class, 'getDetailed']);
Route::get('plots/info/{gid}', [PlotsController::class, 'getInfo']);
Route::put('plots/update/{gid}', [PlotsController::class, 'putUpdate']);
Route::post('plots/update-viewed-plot', [PlotsController::class, 'postUpdateViewedPlot']);
Route::get('plots/mean-history', [PlotsController::class, 'getMeanHistory'])->middleware('ability:view_sat_data');
Route::get('plots/search', [PlotsController::class, 'getSearch']);
Route::get('plots/bin', [PlotsController::class, 'getPlotBin']);
Route::get('plots/details/{plotUuId}', [PlotsController::class, 'getPlotByUuid']);
Route::get('plots/crops', [PlotsController::class, 'getCrops']);
Route::get('plots/{gid}/crop-history', [PlotsController::class, 'getCropHistoryByPlot']);
Route::get('plots/machine-tasks', [PlotsController::class, 'getPlotsMachineTasks']);
Route::post('plots/change-editable-status', [PlotsController::class, 'updatePlotEditableValue']);
Route::get('plots/dates', [PlotsController::class, 'getPlotsDate']);
Route::post('plots/for-edit', [PlotsController::class, 'listPlotsForEdit']);

// NotificationsController
Route::get('notifications', [NotificationsController::class, 'getIndex']);

// UsersController
Route::get('users/cnt-organization-plot-contracts', [UsersController::class, 'countOrganizationsContractsAndPlotsByUser']);
Route::post('users/change-password', [UsersController::class, 'postChangePassword']);
Route::post('users/edit-profile', [UsersController::class, 'postEditProfile']);
Route::post('users/upload-profile-image', [UsersController::class, 'postUploadProfileImage']);
Route::get('users/load-user', [UsersController::class, 'getLoadUser']);
Route::post('users/farms/visibility', [UsersController::class, 'farmVisibility']);
Route::get('users/farms', [UsersController::class, 'farms']);
Route::get('users/farms-by-organization/{organization?}', [UsersController::class, 'getFarmsByOrganization']);
Route::get('users/abilities-and-roles', [UsersController::class, 'getAbilitiesAndRoles']);
Route::get('users/list-by-role', [UsersController::class, 'getUsersByRole']);

// RecommendationsController
Route::get('recommendations/list', [RecommendationsController::class, 'getList']);
Route::get('recommendations/files-list', [RecommendationsController::class, 'getFilesList']);
Route::get('recommendations/download-file', [RecommendationsController::class, 'getDownloadFile']);

// PlotsNotesController
Route::get('plots-notes/list', [PlotsNotesController::class, 'getList']);
Route::post('plots-notes/create-note', [PlotsNotesController::class, 'postCreateNote']);
Route::post('plots-notes/delete-note', [PlotsNotesController::class, 'postDeleteNote']);

// OrganizationController
Route::get('organizations/list', [OrganizationController::class, 'list']);
Route::get('organizations/search', [OrganizationController::class, 'search']);
Route::get('organizations/names', [OrganizationController::class, 'getNameByIdentityNumbers']);
Route::get('organizations/packages/{type?}', [OrganizationController::class, 'listPackagesByOrganization']);
Route::get('organizations/get-missing-technofarm-organizations', [OrganizationController::class, 'getMissingTechnofarmOrganizations']);

Route::apiResource('organizations', 'OrganizationController');
Route::post('organizations/choose/{organization}', [OrganizationController::class, 'choose']);
Route::get('organizations/{organization}/farms', [OrganizationController::class, 'farms']);
Route::get('organizations/{organization}/users', [OrganizationController::class, 'users']);
Route::post('organizations/update/{organization}', [OrganizationController::class, 'updateOrganization']);
Route::get('organizations/{organization}/users-raw', [OrganizationController::class, 'usersRaw']);

Route::prefix('organization')->group(function () {
    // ContactPersonsController
    Route::get('{organization}/contact-persons', [Organization\ContactPersonsController::class, 'list']);
    Route::post('{organization}/contact-persons', [Organization\ContactPersonsController::class, 'create']);
    Route::put('{organization}/contact-persons/{contactPerson}', [Organization\ContactPersonsController::class, 'update']);
    Route::delete('{organization}/contact-persons/{contactPerson}', [Organization\ContactPersonsController::class, 'delete']);
    // AddressController
    Route::get('{organization}/address', [Organization\AddressController::class, 'list']);
    Route::post('{organization}/address', [Organization\AddressController::class, 'create']);
    Route::put('{organization}/address/{address}', [Organization\AddressController::class, 'update']);
    Route::delete('{organization}/address/{address}', [Organization\AddressController::class, 'delete']);
});

// FarmController
Route::apiResource('farms', 'FarmController')->parameters([
    'farms' => 'farm:uuid',
]);

// RoleController
Route::get('roles/{roleName}/abilities', [RoleController::class, 'abilitiesForRole']);

// UserManagementController
Route::get('user-management/users/list', [UserManagementController::class, 'usersList']);
Route::post('user-management/users/create', [UserManagementController::class, 'createUser']);
Route::post('user-management/users/update/{user}', [UserManagementController::class, 'updateUser'])->middleware('can:update,user');
Route::post('user-management/users/toggle/{user}', [UserManagementController::class, 'toggleUserStatus'])->middleware('can:update,user');
Route::get('user-management/users', [UserManagementController::class, 'findUser']);

// PestsDiseaseController
Route::get('pests-diseases/risks', [PestsDiseasesController::class, 'risks']);

Route::prefix('common')->group(function () {
    Route::resource('dealers', \Common\DealersController::class);

    // OrderStatusesController
    Route::resource('order-statuses', \Common\OrderStatusesController::class);

    // CropsController
    Route::get('crops', [Common\CropsController::class, 'getIndex']);
    Route::get('crops/search', [Common\CropsController::class, 'getSearch']);
    Route::get('crops/hybrid', [Common\CropsController::class, 'getHybrid']);
    Route::get('crops/gdd', [Common\CropsController::class, 'getGdd']);
    Route::get('crops/categories', [Common\CropsController::class, 'getCategoriesByCrop']);

    // FarmingYearsController
    Route::get('farming-years', [Common\FarmingYearsController::class, 'index']);
    Route::get('farming-years/organization/{organization?}', [Common\FarmingYearsController::class, 'byOrganization']);

    // LayersController
    Route::get('layers/{id}', [Common\LayersController::class, 'getLayerData']);
    Route::get('layers/{id}/png', [Common\LayersController::class, 'getLayerPng']);

    // EkatteController
    Route::get('ekatte', [Common\EkatteController::class, 'getEkatte']);
});

// StaticMapController
Route::get('static-map', [StaticMapController::class, 'downloadPNG']);

// ProtocolController
Route::get('protocol/packages/generate', [ProtocolController::class, 'generatePackagesProtocol']);
Route::get('protocol/generate/{order}', [ProtocolController::class, 'generateProtocolByOrder']);
Route::get('protocol/{id}/generate', [ProtocolController::class, 'generateProtocolById']);

// IntegrationController
Route::get('integration/contract/{contractId}', [IntegrationController::class, 'showByContract']);
Route::get('integration/organization/{organizationId}', [IntegrationController::class, 'showByOrganization']);
Route::get('integration/history/{integration}', [IntegrationController::class, 'getIntegrationHistory'])->middleware('can:manageIntegration,integration');
Route::post('integration', [IntegrationController::class, 'store'])->middleware('organization-data-access');
Route::put('integration/{integration}', [IntegrationController::class, 'updateIntegration'])->middleware('can:manageIntegration,integration');

// IntegrationAddressController
Route::get('integration-address', [IntegrationAddressController::class, 'index']);
Route::get('integration-address/{integrationAddress}', [IntegrationAddressController::class, 'show']);
Route::post('integration-address', [IntegrationAddressController::class, 'store']);
Route::put('integration-address/{integrationAddress}', [IntegrationAddressController::class, 'update']);

// IrrigationPlatformController
Route::get('irrigation-platform', [IrrigationPlatformController::class, 'get']);
Route::get('irrigation-platform/report', [IrrigationPlatformController::class, 'getIrrigationReport']);
Route::get('irrigation-platform/download-report-list', [IrrigationPlatformController::class, 'downloadReportList']);
Route::get('irrigation-platform/map', [IrrigationPlatformController::class, 'getPlatformsGeoJSON']);
Route::get('irrigation-platform/{irrigationPlatformId}/pivot-position', [IrrigationPlatformController::class, 'getPivotPositionGeoJSON']);
Route::get('irrigation-platform/tasks', [IrrigationPlatformController::class, 'tasks']);
Route::get('irrigation-platform/tasks/{id}/raw', [IrrigationPlatformController::class, 'getTaskRawData']);
Route::get('irrigation-platform/{irrigationPlatformId}', [IrrigationPlatformController::class, 'getById']);
Route::get('irrigation-platform/state/echart', [IrrigationPlatformController::class, 'getIrrigationPlatformStateDataEChart']);
Route::get('irrigation-platform/pivot/state/echart', [IrrigationPlatformController::class, 'getPivotStateDataEChart']);
Route::post('irrigation-platform', [IrrigationPlatformController::class, 'store']);
Route::put('irrigation-platform/{irrigationPlatformId}', [IrrigationPlatformController::class, 'update']);

// IrrigationEventsController
Route::get('irrigation-events/history', [IrrigationEventsController::class, 'history']);
Route::get('irrigation-events/count/echart', [IrrigationEventsController::class, 'getEventsCountEchart']);
Route::get('irrigation-events/avg-rate/echart', [IrrigationEventsController::class, 'getEventsAvgRateEchart']);
Route::get('irrigation-events/report', [IrrigationEventsController::class, 'getReport']);
Route::post('irrigation-events/report/{type}', [IrrigationEventsController::class, 'exportIrrigationReport']);
Route::post('irrigation-events/report/{type}/send', [IrrigationEventsController::class, 'sendIrrigationReport'])->middleware('set-service-provider-outgoing-email');
Route::get('irrigation-events/name', [IrrigationEventsController::class, 'getEventsName']);
Route::post('irrigation-events/reports/schedule/irrigation-per-day', [IrrigationEventsController::class, 'scheduleIrrigationPerDateReport']);

// IrrigationUnitsController
Route::get('irrigation-units/sync', [IrrigationUnitsController::class, 'sync'])->middleware('organization-data-access');
Route::get('irrigation-units', [IrrigationUnitsController::class, 'get'])->middleware('organization-data-access');
Route::put('irrigation-units/{unitId}', [IrrigationUnitsController::class, 'update'])->middleware('organization-data-access');

// MachinesController
Route::get('/machine/state/echart', [MachinesController::class, 'getStateForEchart']);
Route::get('/machine/work-operations/echart', [MachinesController::class, 'getMachinesCountByWorkOperationsEchart']);
Route::get('/machine/map', [MachinesController::class, 'getForMap']);
Route::get('/machine/events', [MachinesController::class, 'getEvents']);
Route::get('/machine/events/{id}/calculate-cultivated-geom', [MachinesController::class, 'calculateCultivatedGeom']);
Route::get('/machine/events/type/echart', [MachinesController::class, 'getEventsTypeEchart']);
Route::get('/machine/events/work-operations/echart', [MachinesController::class, 'getEventsWorkOperationsEchart']);
Route::get('/machine/events/tasks/echart', [MachinesController::class, 'getEventsTasksEchart']);
Route::get('/machine/events/report', [MachinesController::class, 'getEventsReport']);
Route::post('/machine/events/report/{type}', [MachinesController::class, 'exportEventsReport']);
Route::post('/machine/events/report/{type}/send', [MachinesController::class, 'sendEventsReport'])->middleware('set-service-provider-outgoing-email');
Route::post('/machine/events/report/schedule/machine-events', [MachinesController::class, 'scheduleMachineEventsReport']);
Route::get('/machine/events/drivers', [MachinesController::class, 'getDrivers']);
Route::get('/machine/events/types', [MachinesController::class, 'getEventTypes']);

Route::get('/work-operation/{for_instance}', [WorkOperationController::class, 'getWorkOperationsForInstance']);
Route::apiResource('work-operation', 'WorkOperationController');

// MachineUnitsController
Route::get('machine-units', [MachineUnitsController::class, 'get']);
Route::get('machine-units/sync', [MachineUnitsController::class, 'sync']);
Route::get('machine-units/types', [MachineUnitsController::class, 'getTypes']);
Route::get('machine-units/{unit}/track/{format?}', [MachineUnitsController::class, 'getUnitTrack']);
Route::put('machine-units/{unit}', [MachineUnitsController::class, 'update'])->middleware('can:manageMachineUnit,unit');
Route::delete('machine-units/{unit}', [MachineUnitsController::class, 'delete'])->middleware('can:manageMachineUnit,unit');

// MachineImplementController
Route::get('machine-implements', [MachineImplementController::class, 'get']);
Route::get('machine-implements/sync', [MachineImplementController::class, 'sync']);
Route::get('machine-implements/statuses', [MachineImplementController::class, 'getImplementStatuses']);
Route::put('machine-implements/{implement}', [MachineImplementController::class, 'update'])->middleware('can:manageMachineImplement,implement');

// FarmTrackReportsLogController
Route::get('farm-track-reports-log/organization/{organizationId}', [FarmTrackReportsLogController::class, 'getFarmTrackReportsByOrganization']);

Route::apiResource('scheduled-reports', ScheduledReportsController::class);

Route::apiResource('scheduled-reports', 'ScheduledReportsController');

Route::apiResource('crops-rotation', 'CropsRotationController');

Route::get('guidance-line/export', 'GuidanceLineController@export');
Route::apiResource('guidance-line', 'GuidanceLineController');

Route::apiResource('units-of-measure', 'UnitsOfMeasureController')->parameters([
    'units-of-measure' => 'unitOfMeasure',
])->middleware('organization-data-access');
Route::apiResource('unit-of-measure-categories', 'UnitOfMeasureCategoriesController');

Route::apiResource('products-type', 'ProductsTypeController');
Route::apiResource('products', 'ProductsController');

Route::apiResource('active-ingredients', 'ActiveIngredientsController');

Route::get('machine-tasks/timeline', [MachineTasksController::class, 'getMachineTasksTimeline'])->middleware('organization-data-access');
Route::apiResource('machine-tasks', 'MachineTasksController')->middleware('organization-data-access');
Route::get('machine-tasks/work-operations/echart', [MachineTasksController::class, 'getWorkOperationsWithTasksStateChart'])->middleware('organization-data-access');

Route::get('machine-tasks-products/report', [MachineTaskProductsController::class, 'getReport']);
Route::post('machine-tasks-products/report/{type}', [MachineTaskProductsController::class, 'exportReport']);
Route::post('machine-tasks-products/report/{type}/send', [MachineTaskProductsController::class, 'sendReport'])->middleware('set-service-provider-outgoing-email');
