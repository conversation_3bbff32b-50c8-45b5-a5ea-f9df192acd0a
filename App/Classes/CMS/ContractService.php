<?php
/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\CMS;

use App\Models\Plot;
use Auth;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Psr\Http\Message\ResponseInterface;

class ContractService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function list($requestData)
    {
        $query = [
            'filter' => [
                'customerIdentification' => [
                    'type' => 'eq',
                    'x' => $requestData['organization_identity_number'],
                ],
            ],
        ];

        if (isset($requestData['page']) && !empty($requestData['page'])) {
            $query['page'] = $requestData['page'];
        }

        if (isset($requestD<PERSON>['limit']) && !empty($requestData['limit'])) {
            $query['limit'] = (int)$requestData['limit'];
        }

        if (isset($requestData['sort']) && !empty($requestData['sort'])) {
            $query['sort'] = $requestData['sort'];
        }

        if (isset($requestData['order_uuid']) && !empty($requestData['order_uuid'])) {
            $query['filter']['orderUuids'] = [
                'type' => 'in',
                'x' => $requestData['order_uuid'],
            ];
        }

        if (isset($requestData['includeResponsibleServiceUser']) && !empty($requestData['includeResponsibleServiceUser'])) {
            $query['filter']['includeResponsibleServiceUser'] = [
                'type' => 'eq',
                'x' => 'yes',
            ];
        }

        try {
            $response = $this->client->request('GET', 'api/contracts', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Contracts for an organization.
     */
    public function getContractsShortDataByIdentityNumber(array $headerParams)
    {
        return $this->getContractShortData($headerParams);
    }

    /**
     * Receiving contracts and packages for multiple organizations and joining each organization to a concrete contract.
     */
    public function getContractsShortDataByOrganizations(array $headerParams)
    {
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get()->toArray();

        if (isset($headerParams['filter']['customer_identification'])) {
            $identityNumber = $headerParams['filter']['customer_identification'];
            $organizations = (Auth::user()->organizations()->get()->filter(function ($value) use ($identityNumber) {
                return $value->identity_number == $identityNumber;
            }))->toArray();
        }

        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');
        $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);

        $contracts = $this->getContractShortData($headerParams);
        foreach ($organizations as $organization) {
            foreach ($contracts['items'] as $key => $contract) {
                if ($organization['identity_number'] === $contract['customer_identification']) {
                    $contracts['items'][$key]['organization'] = $organization;

                    if (isset($contracts['items'][$key]['packages']) && count($contracts['items'][$key]['packages'])) {
                        usort($contracts['items'][$key]['packages'], function ($item1, $item2) {
                            return $item2['isFullSampling'];
                        });
                    }
                }
            }
        }

        return $contracts;
    }

    /**
     * Count all contracts by organizations.
     *
     * @param $headerParams array
     *
     * @throws GuzzleException
     *
     * @return string
     */
    public function countContracts($organizations, array $headerParams = [])
    {
        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');
        $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);

        try {
            $response = $this->client->request('GET', 'api/contracts/count', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageService
    public function addFieldsToSubscription($plots, $subscriptionId)
    {
        $subscriptionPackageFields = [];
        foreach ($plots as $plot) {
            $subscriptionPackageFields[] = [
                'plotUuid' => $plot->uuid,
                'area' => $plot->area,
            ];
        }

        $params = [
            'subscriptionPackageFields' => $subscriptionPackageFields,
        ];

        try {
            $response = $this->client->request('POST', 'api/contracts/subscription-package/' . $subscriptionId, [
                'headers' => $this->getHeaders(),
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageService this method is the same as addFieldsToSubscription()
    public function addFieldsToService($plots, $serviceId)
    {
        $servicePackageFields = [];
        foreach ($plots as $plot) {
            $servicePackageFields[] = [
                'plotUuid' => $plot->uuid,
                'area' => $plot->area,
            ];
        }

        $params = [
            'servicePackageFields' => $servicePackageFields,
        ];

        try {
            $response = $this->client->request('POST', 'api/contracts/service-package/' . $serviceId, [
                'headers' => $this->getHeaders(),
                'json' => $params,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageService
    public function listPackagesForCard($requestData, $orgIdentityNumber)
    {
        $query = [];

        if (isset($requestData['page']) && !empty($requestData['page'])) {
            $query['page'] = (int)$requestData['page'];
        }

        if (isset($requestData['limit']) && !empty($requestData['limit'])) {
            $query['limit'] = (int)$requestData['limit'];
        }

        try {
            $response = $this->client->request('GET', 'api/contracts/' . $orgIdentityNumber . '/packages', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO:: GPS-1540 rename method and move it in PackageService

    /**
     * @throws GuzzleException
     */
    public function getCmsDataForPackagesByContractIdAndFilter(string $type, array $queryParams, int $contractId = null)
    {
        $url = 'api/contracts/' . $type . '/filter';

        if ($contractId) {
            $url = 'api/contracts/' . $type . '/contract/' . $contractId;
        }

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $queryParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getAllOrdersIdByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/orders', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package-field/orders', $queryParams, $contractId);

        $response = array_merge($subscriptionResponse, $serviceResponse);
        $ordersId = array_column($response, 'orderUuid');
        $ordersId = array_unique($ordersId);

        return array_values($ordersId);
    }

    public function getAllPlotsUuidWithStateByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/plots', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package-field/plots', $queryParams, $contractId);

        return array_merge($subscriptionResponse, $serviceResponse);
    }

    // TODO: GPS-1540 move it in PackageService
    public function getAllServiceOrSubscriptionPackagesByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package', $queryParams, $contractId);

        return array_merge(($subscriptionResponse ?? []), ($serviceResponse ?? []));
    }

    // TODO: GPS-1540 move it in PackageFieldService

    /**
     * @throws GuzzleException
     *
     * @return mixed|ResponseInterface
     */
    public function savePackageFields(string $contractType, array $packageFieldData)
    {
        try {
            $response = $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/', [
                'headers' => $this->getHeaders(),
                'json' => $packageFieldData,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageFieldService
    public function updateSubscriptionPackageFieldStateWhenSelectedSampler(string $contractType, array $plotsData)
    {
        try {
            $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/selected-sampler', [
                'headers' => $this->getHeaders(),
                'json' => $plotsData,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    // TODO: GPS-1540 move it in PackageFieldService
    public function updateSubscriptionPackageFieldStateWhenSyncedToMobile(string $contractType, array $data)
    {
        try {
            $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/synced-to-mobile', [
                'headers' => $this->getHeaders(),
                'json' => $data,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    // TODO:: GPS-1540 this method is not used anywhere
    public function getContractById(string $id)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/' . $id, [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getContractShortData($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/short', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCountAmountsForContracts($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/count/amount', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function hasPackage($contractId, $status, $slugShort)
    {
        $query = ['status' => $status, 'slugShort' => $slugShort];

        try {
            $response = $this->client->request('GET', 'api/contracts/' . $contractId . '/has-package', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getStationsAmountByOrganizations($customerIdentifications, $withPagination, $offset, $limit)
    {
        $query = [
            'customer_identifications' => $customerIdentifications,
            'withPagination' => $withPagination,
            'offset' => $offset,
            'limit' => $limit,
        ];

        try {
            $response = $this->client->request('GET', 'api/contracts/stations/amount', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getStationContractsByOrganizations($customerIdentifications)
    {
        $query = [
            'customer_identifications' => $customerIdentifications,
        ];

        try {
            $response = $this->client->request('GET', 'api/stations/contracts', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getOverlapContracts($contractId, $status, $hasStation = true)
    {
        $query = ['status' => $status, 'has_station' => $hasStation];

        try {
            $response = $this->client->request('GET', 'api/contracts/' . $contractId . '/get-overlap', [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getPlotsOverlapBySubscriptionContract(array $plotUuids, int $contractId, int $organizationId, string $lang = 'en'): array
    {
        $queryParams['filter'] = [
            'is_full_sampling' => true,
        ];

        $contractPlotUuids = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/plots', $queryParams, $contractId);
        $contractPlotUuids = array_column($contractPlotUuids, 'plotUuid');
        $contractPlotUuids = array_values(array_unique($contractPlotUuids));

        $contractOrderUuids = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/orders', $queryParams, $contractId);
        $contractOrderUuids = array_column($contractOrderUuids, 'orderUuid');
        $contractOrderUuids = array_values(array_unique($contractOrderUuids));

        return Plot::getPlotsOverlapByContract($plotUuids, $contractPlotUuids, $contractOrderUuids, $organizationId, $lang);
    }

    public function getContractFieldsByPackage(int $contractId, string $contractType, int $packageId)
    {
        $query = ['packageId' => $packageId];

        try {
            $response = $this->client->get("api/contracts/{$contractId}/{$contractType}/fields", [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function removeFieldsFromContract(int $contractId, array $orderUuids, array $plotUuids)
    {
        $query = [
            'orderUuids' => json_encode($orderUuids),
            'plotUuids' => json_encode($plotUuids),
        ];

        try {
            $response = $this->client->delete("api/contracts/{$contractId}/fields", [
                'headers' => $this->getHeaders(),
                'query' => $query,
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
