<?php

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 *
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Services\Plot;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\PlotService as PlotHttpService;
use App\Classes\Echarts\EChartsCropDevelopmentFormatter;
use App\Exceptions\ForbiddenException;
use App\Helpers\Helper;
use App\Jobs\UpdateFieldFarmIdInCMS;
use App\Models\API\OrdersTypeMapping;
use App\Models\CropCategory;
use App\Models\CropHybrid;
use App\Models\Farm;
use App\Models\IrrigationEventPlot;
use App\Models\LayerPlot;
use App\Models\LayerPlotFile;
use App\Models\MachineEvent;
use App\Models\MachineTask;
use App\Models\Order;
use App\Models\OrderFile;
use App\Models\OrderPlotRel;
use App\Models\Organization;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Models\StaticModels\FarmingYear;
use App\Models\User;
use App\Models\UserStation;
use App\Services\Crop\CropService;
use DateTime;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use hanneskod\classtools\Exception\LogicException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class PlotService
{
    private $contractService;
    private $cropService;
    private $plotHttpService;

    public function __construct(ContractService $contractService, CropService $cropService, PlotHttpService $plotHttpService)
    {
        $this->contractService = $contractService;
        $this->cropService = $cropService;
        $this->plotHttpService = $plotHttpService;
    }

    public static function getPlotsArea($fields)
    {
        return Plot::whereIn('gid', $fields)
            ->get(['farm_id', 'gid', 'uuid', DB::raw('round((ST_Area(geom)/1000)::numeric, 3) as area')]);
    }

    public function plotsForApproveExtent($userId, $proj = 4326)
    {
        $query = Plot::selectRaw('ST_Extent(ST_Transform(su_satellite_plots.geom, ?::integer)) as extent', [$proj])
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms AS f', 'f.id', '=', 'uf.farm_id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', '=', 'su_satellite_plots.uuid')
            ->join('su_satellite_orders AS so', 'so.uuid', '=', 'sopr.order_uuid')
            ->where('uf.user_id', $userId);

        $extent = [];
        $queryResult = $query->first();

        if (!$queryResult) {
            return $extent;
        }

        $box2D = $queryResult->extent;
        preg_match_all("!\d+.\d+!", $box2D, $extent);

        return array_map('floatval', $extent[0]);
    }

    public function getPlotWithOrganizationAndPackages(string $plotUuId)
    {
        $plot = Plot::select('su_satellite_plots.uuid', 'su_satellite_plots.area', 'su_satellite_plots.name', 'crop_code.crop_name as cropName', 'f.organization_id as organizationId')
            ->join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->leftJoin('su_satellite_plots_crops AS pc', 'pc.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_crop_codes AS crop_code', 'crop_code.id', '=', 'pc.crop_id')
            ->where('su_satellite_plots.uuid', $plotUuId)
            ->first()->toArray();

        $plot['organization'] = Organization::findOrFail($plot['organizationId'])->toArray();
        unset($plot['organizationId']);
        $soilPackages = OrdersTypeMapping::select('cms_type')->where('order_type', 'ILIKE', 'soil')->get()->toArray();
        $soilPackagesSlugs = array_column(json_decode(json_encode($soilPackages), true), 'cms_type');

        $headerParams = [];
        $headerParams['filter']['package_slug'] = json_encode($soilPackagesSlugs);
        $headerParams['filter']['customer_identification'] = json_encode($plot['organization']['identity_number']);
        $headerParams['filter']['plot_uuid'] = $plotUuId;

        $plot['packages'] = $this->contractService->getAllServiceOrSubscriptionPackagesByContractId(null, $headerParams);

        return $plot;
    }

    /**
     * @return array
     */
    public static function calculatePlotsArea($plotsId, $pricePerDka)
    {
        $plots = [];
        $wholeArea = 0;
        $wholePrice = 0;
        $plotsArea = self::getPlotsArea($plotsId);
        for ($i = 0; $i < count($plotsArea); $i++) {
            $area = $plotsArea[$i]->area;
            $plotId = $plotsArea[$i]->gid;
            $price = round($area * $pricePerDka, 2);

            $plots[$plotId] = [
                'price' => $price,
                'uuid' => $plotsArea[$i]->uuid,
            ];
            $wholePrice += $price;
            $wholeArea += $area;
        }

        return [
            'wholeArea' => $wholeArea,
            'wholePrice' => $wholePrice,
            'plots' => $plots,
        ];
    }

    public function getMapCountsForApprove($plotsCms)
    {
        return $this->plotsForApprove($plotsCms);
    }

    public function getHeaderCountsForApprove($plotsCms)
    {
        $arrResult = $this->plotsForApprove($plotsCms, false);

        $arrCounts = array_column($arrResult, 'count');

        return ['plots' => count($arrResult), 'analysis' => array_sum($arrCounts)];
    }

    public function getPlotGeoJson(string $plotUuid)
    {
        return Plot::select(DB::raw("json_build_object(
                'type', 'FeatureCollection',
                'bbox', concat('[',concat_ws(',', ST_XMin(ST_Transform(geom, 4326)), ST_YMin(ST_Transform(geom, 4326)), ST_XMax(ST_Transform(geom, 4326)), ST_YMax(ST_Transform(geom, 4326))),']'),
                'features', json_agg(
                    json_build_object(
                        'type',       'Feature',
                        'id',         uuid,
                        'geometry',   ST_AsGeoJSON(ST_Transform(geom,4326))::json,
                        'properties', json_build_object(
                            'uuid', uuid,
                            'gid', gid
                        )
                    )
                )
            ) as geojson"))
            ->where('uuid', $plotUuid)
            ->groupBy('su_satellite_plots.geom')
            ->get()->pluck('geojson')->first();
    }

    public static function getCropDevelopmentEChartData(
        int $farmYear,
        string $from,
        string $to,
        array $cropIds = [],
        array $farmIds = [],
        array $plotIds = [],
        bool $withStationsData = false,
        ?int $maxCloudPercentage = 100
    ) {
        $startFarmingYear = (new DateTime($farmYear - 1 . '-10-01 00:00:00'))->getTimestamp();
        $endFarmingYear = (new DateTime($farmYear . '-09-30 23:59:59'))->getTimestamp();

        if ($to > $endFarmingYear || $to < $startFarmingYear || $from > $endFarmingYear) {
            return Response::json(['error' => 'Dates are out of farming year rage.'], 400);
        }

        if ($from < $startFarmingYear) {
            $toDateMinusFourteenDays = (new DateTime())->setTimestamp($to)->modify('-14 day')->getTimestamp();
            $from = $startFarmingYear > $toDateMinusFourteenDays ? $startFarmingYear : $toDateMinusFourteenDays;
        }

        $chartData = new EChartsCropDevelopmentFormatter();
        $model = new LayerPlot();
        $dataAvg = $model->getAvgMeanForDate($farmYear, $from, $to, $cropIds, $farmIds, $plotIds, $maxCloudPercentage);

        if (!$withStationsData) {
            return $chartData->formatCropDevelopment([], $dataAvg);
        }

        // TODO: GPS-2148 Filter the station data by the dates from dataAvg.
        $stationIds = UserStation::getForCropDevelopment($farmIds, $plotIds);
        $feed = 'daily';
        $period = 'custom';
        $sensors = ['precipitation_sum', 'air_temperature_min', 'air_temperature_max'];
        $result = [];
        foreach ($stationIds as $stationId) {
            try {
                $stationObject = UserStation::where('id', $stationId)->first()->getStationApi();
                $data = $stationObject->getStationReportEChart($feed, $period, $sensors, $from, $to, true);
            } catch (Exception $e) {
                continue;
            }
            $result[] = $data;
        }

        if (!$result) {
            return Response::json(['error' => 'Data not found'], 404);
        }

        return $chartData->formatCropDevelopment($result, $dataAvg);
    }

    public function searchData(?string $name, ?string $year, array $farmIds, ?int $organizationId, int $limit)
    {
        $dataQuery = Plot::basicDataQuery($year);
        $dataQuery->join('su_satellite_orders_plots_rel', 'su_satellite_orders_plots_rel.plot_id', '=', 'su_satellite_plots.gid');
        $dataQuery->where('uf.user_id', Auth::user()->id);

        if ($organizationId) {
            $dataQuery->where('f.organization_id', $organizationId);
        }

        if (count($farmIds) > 0) {
            $dataQuery->whereIn('f.id', $farmIds);
        }

        $crop_name = Config::get('globals.CROP_NAME');

        if ($name) {
            $dataQuery->where(function ($query) use ($name, $crop_name) {
                $query->where('su_satellite_plots.name', 'ILIKE', trim('%' . $name . '%'))
                    ->orWhere('c.' . $crop_name, 'ILIKE', trim('%' . $name . '%'));
            });

            $dataQuery->orderByRaw("su_satellite_plots.name ILIKE '{$name}' desc");
        }

        $dataQuery->groupBy(
            'su_satellite_plots.gid',
            'c.' . $crop_name,
            'uf.user_id'
        );

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return $dataQuery->paginate(
            $limit,
            [
                DB::raw('gid'),
                DB::raw("round((area*{$areaCoef})::numeric, 3) as area"),
                'su_satellite_plots.name',
                'user_id',
                'c.' . $crop_name . ' as crop_name',
                'su_satellite_plots.uuid',
                'thumbnail',
            ]
        );
    }

    /**
     * @return array
     */
    public function searchDataByOrganizationAndContract($name, $organizationId, $contractId, $startDate, $endDate, $limit = 10)
    {
        $queryParams['filter']['packages_state'] = '["In progress","Waiting for plots"]';
        $queryParams['filter']['contain_fields'] = 1;
        $arrOrdersUuids = $this->contractService->getAllOrdersIdByContractId($contractId, $queryParams);
        $excludePlots = OrderPlotRel::select(DB::raw('DISTINCT plot_id'))->whereIn('order_uuid', $arrOrdersUuids)->get()->toArray();

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $query = Plot::getPlotFarmsQuery()->select(
            DB::raw('DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid'),
            DB::raw("round((su_satellite_plots.area*{$areaCoef})::numeric, 3) AS area"),
            'su_satellite_plots.name',
            'su_satellite_plots.farm_id',
            DB::raw('json_build_array(st_xmin(su_satellite_plots.geom),st_ymin(su_satellite_plots.geom),st_xmax(su_satellite_plots.geom),st_ymax(su_satellite_plots.geom)) as extent'),
            'crop_code.crop_name'
        )
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', $organizationId)
            ->whereNotIn('su_satellite_plots.gid', $excludePlots);

        if ($name && '' !== trim($name)) {
            $query->where('su_satellite_plots.name', 'ILIKE', '%' . trim($name) . '%');
        }

        if ($startDate && $endDate) {
            $query->whereBetween('su_satellite_plots.upload_date', [$startDate, $endDate]);
        }

        $plots = $query->paginate($limit);

        return [
            'total' => $plots->total(),
            'rows' => $plots->items(),
        ];
    }

    public function featureData(float $longitude, float $latitude, array $filters, string $language, ?string $orderType = Order::TYPE_INDEX)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = Plot::getFilteredPlotsQuery($filters, $orderType);
        $query->leftJoin('su_farms AS sf', 'sf.id', '=', 'sfu.farm_id');

        $query->select(
            DB::raw('DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid'),
            'su_satellite_plots.geom',
            DB::raw("round((su_satellite_plots.area*{$areaCoef})::numeric, 3) AS area"),
            'su_satellite_plots.name',
            DB::raw("
                COALESCE(jsonb_agg(distinct jsonb_build_object(
                    'crop_name', scc.crop_name_{$language},
                    'sowing_date', sspc.from_date,
                    'harvest_date', sspc.to_date,
                    'irrigated', sspc.irrigated,
                    'is_primary', sspc.is_primary
                ))FILTER (WHERE sspc.crop_id NOTNULL),
                    '[]'
                ) as crops
            "),
            DB::raw('sslp.mean as avg_index'),
            'sspf.web_path',
            'su_satellite_plots.thumbnail',
            'sf.id as farm_id',
            'sf.name as farm_name'
        )
            ->whereRaw(
                'ST_Contains(su_satellite_plots.geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            )
            ->groupBy('gid', 'geom', "scc.crop_name_{$language}", 'sspc.from_date', 'sspc.to_date', 'sspc.irrigated', 'web_path', 'avg_index', 'sspc.is_primary', 'sslp.date', 'sf.id', 'sf.name')
            ->orderBy('gid')
            ->orderBy('sspc.is_primary', 'DESC')
            ->orderBy('sslp.date', 'DESC');

        $featuresCollection = Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce(
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                    'gid' , gid,
                                    'is_ordered' , true,
                                    'area' , area,
                                    'name' , name,
                                    'crops' , crops,
                                    'avg_index' , avg_index,
                                    'web_path' , web_path,
                                    'thumbnail', thumbnail,
                                    'farm', json_build_object(
                                    'id', farm_id, 
                                    'name', farm_name)
                                )
                            )
                        ),
                         '[]'::json
                     )
                ) as \"GeoJSON\"")
            )->first();

        return $featuresCollection->GeoJSON;
    }

    /**
     * Get plots FeatureCollection by filter.
     *
     * @param $layerName
     * @param ?string $extent
     */
    public function getPlotsFeatureData(float $longitude, float $latitude, string $identityNumber, array $filter = [], ?string $extent = null)
    {
        if (Auth::user()->is_demo) {
            return [
                'type' => 'FeatureCollection',
                'features' => [],
            ];
        }

        $organization = Auth::user()->organizations()->where('identity_number', $identityNumber)->first();
        $organizationId = $organization->id;

        $query = Plot::getPlotDataQuery()
            ->addSelect(
                'su_satellite_plots.is_editable',
                'f.id as farm_id',
                'f.name as farm_name',
                'geom'
            )
            ->where('f.organization_id', $organizationId);

        if (array_key_exists('is_editable', $filter)) {
            $query->where('su_satellite_plots.is_editable', (bool)$filter['is_editable']);
        }

        if (isset($filter['start_date'], $filter['end_date'])) {
            $query->whereBetween(DB::raw('su_satellite_plots.upload_date::date'), [$filter['start_date'], $filter['end_date']]);
        }

        // Get Features Data in "new-order"
        if (array_key_exists('order_uuids', $filter)) {
            $query->where('f.organization_id', $organizationId)
                ->whereNotIn(
                    'su_satellite_plots.gid',
                    DB::table('su_satellite_orders_plots_rel as sopr')
                        ->whereIn('sopr.order_uuid', $filter['order_uuids'])
                        ->pluck('plot_id')
                );
        }

        if (array_key_exists('start_date', $filter) && array_key_exists('end_date', $filter)) {
            $query->whereBetween('su_satellite_plots.upload_date', [$filter['start_date'], $filter['end_date']]);
        }

        if ($extent) {
            $query->whereRaw(
                'ST_Intersects(ST_GeomFromText(?, ' . Config::get('globals.DEFAULT_DB_CRS') . '), geom)',
                [$extent]
            );
        } else {
            $query->whereRaw(
                'ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            );
        }

        $query->groupBy('su_satellite_plots.gid', 'crop', 'pc.id', 'f.id', 'f.name');

        $featuresCollection = Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce (
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                'gid' , id,
                                'uuid' , plot_uuid,
                                'area' , area,
                                'name' , name,
                                'is_editable', is_editable,
                                'crop' , crop,
                                'sowing_date' , sowing_date,
                                'harvest_date' , harvest_date,
                                'upload_date' , upload_date,
                                'irrigated' , irrigated,
                                'avg_index' , avg_index,
                                'farm', json_build_object(
                                'id', farm_id,
                                'name', farm_name)
                                )
                            )
                        ),
                        '[]'::json
                     )
                ) as \"GeoJSON\"")
            )->first();

        return $featuresCollection->GeoJSON;
    }

    /**
     * @param string $layerName
     * @param ?string $extent
     */
    public function layerApproveResultsData(float $longitude, float $latitude, ?string $extent = null)
    {
        $query = Plot::getPlotDataQuery()
            ->addSelect('geom')
            ->where('o.type', 'soil')
            ->groupBy('su_satellite_plots.gid', 'crop', 'pc.id');

        if ($extent) {
            $query->whereRaw(
                'ST_Intersects(ST_GeomFromText(?, ' . Config::get('globals.DEFAULT_DB_CRS') . '), geom)',
                [$extent]
            );
        } else {
            $query->whereRaw(
                'ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            );
        }

        $featuresCollection = Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce(
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                'gid' , id,
                                'uuid' , plot_uuid,
                                'area' , area,
                                'name' , name,
                                'crop' , crop,
                                'sowing_date' , sowing_date,
                                'harvest_date' , harvest_date,
                                'irrigated' , irrigated,
                                'avg_index' , avg_index
                                )
                            )
                        ),
                        '[]'::json
                    )
                ) as \"GeoJSON\"")
            )->first();

        return $featuresCollection->GeoJSON;
    }

    public function layerEditablePlotsBoundaries(float $longitude, float $latitude, string $identityNumber, string $startDate, string $endDate, array $farmIds, ?bool $plotIsEditable, ?string $extent = null)
    {
        $query = Plot::select(
            'gid',
            DB::raw('ST_AsGeoJSON(geom)::json as geom')
        )
            ->join('su_farms AS sf', 'sf.id', 'su_satellite_plots.farm_id')
            ->join('su_organizations AS so', 'so.id', '=', 'sf.organization_id')
            ->where('so.identity_number', $identityNumber);

        if (is_bool($plotIsEditable)) {
            $query->where('su_satellite_plots.is_editable', $plotIsEditable);
        }

        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('su_satellite_plots.upload_date::date'), [$startDate, $endDate]);
        }

        if (count($farmIds) > 0) {
            $query->whereIn('sf.id', $farmIds);
        }

        if ($extent) {
            $query->whereRaw(
                'ST_Intersects(ST_GeomFromText(?, ' . Config::get('globals.DEFAULT_DB_CRS') . '), geom)',
                [$extent]
            );
        } else {
            $query->whereRaw(
                'ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            );
        }

        return Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce(
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                    'gid' , gid
                                )
                            )
                        ),
                        '[]'::json
                    )
                ) as \"GeoJSON\"")
            )->pluck('GeoJSON')->first();
    }

    /**
     * Get Features Data in "new-order" Map ModeGet Features Data in "new-order" Map Mode.
     *
     * @param ?string $year
     * @param ?array $gids
     * @param ?string $gid
     * @param ?string $extent
     *
     * @return mixed
     *
     * //TODO:: this method is used only for A2
     */
    public function featuresDataNewOrder(string $layerName, float $longitude, float $latitude, ?string $year, ?array $gids, ?string $gid, ?string $extent)
    {
        if (Auth::user()->is_demo) {
            return [
                'type' => 'FeatureCollection',
                'features' => [],
            ];
        }

        $toDate = $fromDate = '';

        if ($year) {
            $fromDate = ($year - 1) . '-10-01';
            $toDate = $year . '-09-30';
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $crop_name = Config::get('globals.CROP_NAME');

        $userId = Auth::user()->id;
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $query = Plot::join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms_users as uf', 'uf.farm_id', '=', 'f.id')
            ->where('uf.user_id', $userId)
            ->where('uf.is_visible', true)
            ->where('f.organization_id', $organizationId)
            ->selectRaw("gid, su_satellite_plots.name, ST_AsGeoJSON(geom)::json as geom, round((area * {$areaCoef})::numeric, 3) as area, (array_agg(c." . $crop_name . ' ORDER BY spc.year DESC))[1] AS crop')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('spc.is_primary', '=', true);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->groupBy('gid');

        if ($gids) {
            $query->whereIn('gid', $gids);
        } elseif ($extent) {
            $query->whereRaw(
                'ST_Intersects(ST_GeomFromText(?, ' . Config::get('globals.DEFAULT_DB_CRS') . '), geom)',
                [$extent]
            );
        } else {
            if ($gid) {
                $longitude = '';
                $latitude = '';
                $arrResult = $this->longitudeAndLatitude($gid);
                if ($arrResult && isset($arrResult['longitude']) && $arrResult['latitude']) {
                    $longitude = $arrResult['longitude'];
                    $latitude = $arrResult['latitude'];
                }
            }
            $query->whereRaw(
                'ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            );
        }

        if (strlen($fromDate) && strlen($toDate)) {
            $query->where(DB::raw('su_satellite_plots.upload_date::DATE'), '>=', $fromDate)
                ->where(DB::raw('su_satellite_plots.upload_date::DATE'), '<=', $toDate);
        }

        $featuresCollection = Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce(
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                'gid' , gid,
                                'area' , area,
                                'name' , name,
                                'crop' , crop
                                )
                            )
                        ),
                        '[]'::json
                     )
                ) as \"GeoJSON\"")
            )->first();

        $featuresCollection['layerName'] = $layerName;

        return $featuresCollection;
    }

    /**
     * Get Features Data in "edit" Map Mode.
     *
     * @param ?string $gid
     * @param ?string $extent
     *
     * @return mixed
     *
     * //TODO:: this method is used only for A2
     */
    public function featuresDataEdit(string $layerName, float $longitude, float $latitude, ?string $gid, ?string $extent)
    {
        if (Auth::user()->is_demo) {
            return [
                'type' => 'FeatureCollection',
                'features' => [],
            ];
        }

        $userId = Auth::user()->id;
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $query = Plot::join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms_users as uf', 'uf.farm_id', '=', 'f.id')
            ->where('uf.user_id', $userId)
            ->where('uf.is_visible', true)
            ->where('f.organization_id', $organizationId)
            ->whereDoesntHave('orders', function ($q) {
                $q->where('status', '<>', 'canceled');
            })
            ->selectRaw('su_satellite_plots.gid, su_satellite_plots.name, ST_AsGeoJSON(su_satellite_plots.geom)::json as geom, uf.farm_id');

        if ($extent) {
            $query->whereRaw(
                'ST_Intersects(ST_GeomFromText(?, ' . Config::get('globals.DEFAULT_DB_CRS') . '), geom)',
                [$extent]
            );
        } else {
            if ($gid) {
                $longitude = '';
                $latitude = '';
                $arrResult = $this->longitudeAndLatitude($gid);

                if ($arrResult && isset($arrResult['longitude']) && $arrResult['latitude']) {
                    $longitude = $arrResult['longitude'];
                    $latitude = $arrResult['latitude'];
                }
            }

            $query->whereRaw(
                'ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), ' . Config::get('globals.DEFAULT_DB_CRS') . '))',
                [$longitude, $latitude]
            );
        }

        $featuresCollection = Plot::from('geom')
            ->withExpression('geom', $query)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', coalesce(
                        json_agg(
                            json_build_object(
                                'type', 'Feature',
                                'geometry', geom,
                                'properties', json_build_object(
                                'gid' , gid,
                                'farm_id' , farm_id,
                                'name' , name,
                                'is_ordered' , false
                                )
                            )
                        ),
                        '[]'::json
                     )
                ) as \"GeoJSON\"")
            )->first();

        $featuresCollection['layerName'] = $layerName;

        return $featuresCollection;
    }

    /**
     * @param array $plotUuids
     */
    public function getPlotsByOrderUuidsAndPlotUuids(array $orderUuids, array $plots)
    {
        $returnData = [];

        $plotUuids = array_column($plots, 'plotUuid');
        $plots = array_column($plots, null, 'plotUuid');

        $query = Plot::getPlotDataQuery()
            ->addSelect(
                'o.uuid as orderUuid',
                'su_satellite_plots.uuid as plotUuid',
                'sopr.demo_sampling as demoSampling',
                DB::raw('ST_Transform(geom,3857) as geom'),
            )
            ->whereIn('o.uuid', $orderUuids)
            ->groupBy('su_satellite_plots.gid', 'crop', 'pc.id', 'o.uuid', 'sopr.demo_sampling');
        $result = $query->whereIn('su_satellite_plots.uuid', $plotUuids)->get()->toArray();

        foreach ($result as $row) {
            $row['fieldState'] = $plots[$row['plot_uuid']]['fieldState'];
            $returnData[$row['fieldState']][] = $row;
        }

        return $returnData;
    }

    public function createNewPlots(Farm $farm, string $plotName, array $geoms)
    {
        $plotGids = collect();
        for ($i = 0; $i < count($geoms); $i++) {
            $geom = $geoms[$i];
            $plot = new Plot();
            $plot->area = DB::raw("round((ST_Area(ST_GeomFromText('{$geom}', " . Config::get('globals.DEFAULT_DB_CRS') . '))/1000)::numeric, 3)');
            $plot->geom = DB::raw("ST_GeomFromText('{$geom}', " . Config::get('globals.DEFAULT_DB_CRS') . ')');
            $plot->name = $plotName;
            $plot->farm()->associate($farm);
            $plot->save();
            $plotGids->push($plot->gid);
        }

        return true;
    }

    /**
     * @param null|int $plotId
     * @param null|array $crops
     * @param ?string $plotName
     * @param ?Farm $farm
     * @param ?array $geoms
     *
     * @throws Exception
     */
    public function updateExistingPlot(int $plotId, ?string $plotName, ?Farm $farm, ?array $geoms, array $crops = [], bool $irrigated = null, int $farmYear = null)
    {
        DB::beginTransaction();

        try {
            $this->updatePlotHelper($plotId, $plotName, $farm, $geoms, $crops, $irrigated, $farmYear);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return true;
    }

    public function updateMultiplePlots(array $plots): bool
    {
        DB::beginTransaction();

        try {
            foreach ($plots as $plot) {
                $farm = Farm::findOrFail($plot['farm_id']);
                $this->updatePlotHelper($plot['gid'], $plot['name'], $farm, [], $plot['crops'], $plot['irrigated'], $plot['farm_year']);
            }
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
        DB::commit();

        return true;
    }

    /**
     * @param ?string $fromDate
     * @param ?string $toDate
     * @param ?string $element
     * @param ?bool $withChartData
     * @param ?int $maxCloudPercentage
     *
     * @throws ForbiddenException
     */
    public function getHistory(
        int $plotId,
        string $type,
        int $organizationId,
        string $sort,
        string $sortDirection,
        ?string $fromDate,
        ?string $toDate,
        ?string $element,
        ?bool $withChartData = true,
        ?int $maxCloudPercentage = 100
    ) {
        if (!Auth::user()->globalUser()->can('view_sat_data') && ('index' == $type || 'soil' == $type)) {
            throw new ForbiddenException();
        }

        $historyCollection = $this->getHistoryCollection($plotId, $type, $organizationId, $sort, $sortDirection, $fromDate, $toDate, $element, $maxCloudPercentage);

        if (!$withChartData) {
            return $historyCollection;
        }

        return $this->addChartData($historyCollection, $fromDate, $toDate);
    }

    public function getContractFieldsByPackage(int $contractId, string $contractType, int $packageId)
    {
        $fields = $this->contractService->getContractFieldsByPackage($contractId, $contractType, $packageId);

        if (!is_array($fields) || (is_array($fields) && 0 === count($fields))) {
            return [];
        }

        $plotUuids = array_column($fields, 'plotUuid') ?? [];
        $orderUuids = array_column($fields, 'orderUuid') ?? [];

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $plotsQuery = Plot::from('su_satellite_plots AS sp')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', '=', 'sp.uuid')
            ->join('su_satellite_orders AS so', 'so.uuid', '=', 'sopr.order_uuid')
            ->join('su_farms as f', 'f.id', '=', 'sp.farm_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'f.id')
            ->whereIn('sopr.order_uuid', $orderUuids)
            ->whereIn('sopr.plot_uuid', $plotUuids)
            ->where('uf.user_id', Auth::user()->id)
            ->where('uf.is_visible', true)
            ->select(
                'sp.gid',
                'sp.uuid',
                'sp.name',
                'sp.geom',
                'so.from_date AS order_from_date',
                'so.to_date AS order_to_date',
                DB::raw("round((sp.area*{$areaCoef})::numeric, 3) AS area"),
                DB::raw('
                    JSONB_BUILD_OBJECT(
                        \'id\', f.id,
                        \'name\', f.name,
                        \'organization_id\', f.organization_id
                    ) AS farm
                ')
            )
            ->groupBy(
                'sp.gid',
                'f.id',
                'so.from_date',
                'so.to_date'
            )
            ->distinct();

        $plotsMachineEventsQuery = Plot::from('plots')
            ->leftJoin('su_machine_events AS sme', 'sme.plot_id', '=', 'plots.gid')
            ->whereBetween('sme.date', [DB::raw('plots.order_from_date'), DB::raw('plots.order_to_date')])
            ->select(
                'plots.gid AS plot_id',
                DB::raw('COUNT(sme.id) AS events_count')
            )
            ->groupBy('plots.gid');

        $plotsIrrigationEventsQuery = Plot::from('plots')
            ->leftJoin('su_irrigation_events_plots AS siep', 'siep.plot_id', '=', 'plots.gid')
            ->leftJoin('su_irrigation_events AS sie', 'sie.id', '=', 'siep.event_id')
            ->whereBetween('sie.date', [DB::raw('plots.order_from_date'), DB::raw('plots.order_to_date')])
            ->select(
                'plots.gid AS plot_id',
                DB::raw('COUNT(siep.id) AS events_count')
            )
            ->groupBy('plots.gid');

        $query = Plot::withExpression('plots', $plotsQuery)
            ->withExpression('plots_machine_events', $plotsMachineEventsQuery)
            ->withExpression('plots_irrigation_events', $plotsIrrigationEventsQuery)
            ->from('plots')
            ->leftJoin('plots_machine_events AS pme', 'pme.plot_id', '=', 'plots.gid')
            ->leftJoin('plots_irrigation_events AS pie', 'pie.plot_id', '=', 'plots.gid')
            ->select(
                DB::raw('ST_Extent(ST_Transform(plots.geom, 4326)) as extent'),
                DB::raw("JSONB_AGG(
                    JSONB_BUILD_OBJECT(
                        'gid', plots.gid,
                        'uuid', plots.uuid,
                        'name', plots.name,
                        'area', plots.area,
                        'geom_json', ST_AsGeoJSON(ST_Transform(plots.geom, 4326))::json,
                        'farm', plots.farm,
                        'machine_events_count', COALESCE(pme.events_count, 0),
                        'irrigation_events_count', COALESCE(pie.events_count, 0)
                    )
                ) as plots")
            );

        $contractFields = $query->first()->toArray();
        $plots = json_decode($contractFields['plots'], true);

        if (!$plots) {
            return [
                'extent' => null,
                'plots' => [],
            ];
        }

        $plots = array_map(function ($plot) use ($plotUuids, $fields) {
            $plotIndex = array_search($plot['uuid'], $plotUuids);
            $plot['can_remove'] = $fields[$plotIndex]['canRemove'];
            $plot['state'] = $fields[$plotIndex]['fieldState'];

            return $plot;
        }, $plots);

        return [
            'extent' => Helper::parseExtentToOpenLayer($contractFields['extent'], true),
            'plots' => $plots,
        ];
    }

    public function removeFieldsFromContract(int $contractId, array $orderUuids, array $plotUuids)
    {
        $removedFieldsCount = 0;
        $ordersFiles = [];
        $layerPlotsFiles = [];

        DB::beginTransaction();

        try {
            MachineTask::removeByOrderUuidsAndPlotUuids($plotUuids, $orderUuids);
            MachineEvent::removeByOrderUuidsAndPlotUuids($plotUuids, $orderUuids);
            IrrigationEventPlot::removeByOrderUuidsAndPlotUuids($plotUuids, $orderUuids);

            $removedFieldsCount = OrderPlotRel::removeByUuids($orderUuids, $plotUuids);
            $ordersWithoutPlots = OrderPlotRel::getOrdersWithoutPlots($orderUuids);
            $ordersFiles = OrderFile::getFilePathsByOrderIds($ordersWithoutPlots)->get()->pluck('path')->toArray();
            $layerPlotsFiles = LayerPlotFile::getLayerPlotFilePathsByOrderIds($ordersWithoutPlots)->get()->pluck('path')->toArray();

            // Delete orders, orders files, layers plots and layers plots files (using onDelete='cascade')
            Order::whereIn('id', $ordersWithoutPlots)->delete();

            // Delete fields from cms
            $this->contractService->removeFieldsFromContract($contractId, $orderUuids, $plotUuids);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            if (404 === $e->getCode() || 409 === $e->getCode()) {
                return $removedFieldsCount;
            }

            throw $e;
        }

        // Remove orders files from the disc
        File::delete($ordersFiles);

        // Remove layer plots files from the disc
        File::delete($layerPlotsFiles);
    }

    /**
     * @param null|mixed $cropIds
     * @param null|mixed $cropHybridId
     *
     * @return array
     */
    public function getPlotsAvgData($cropIds = null, $farmYears = [], $sort = 'date', $cropHybridId = null)
    {
        $plotsDataQuery = LayerPlot::getDataQuery()->select([
            'crop_id',
            'su_satellite_layers_plots.date',
            DB::raw('round(avg(mean)) AS mean'),
            DB::raw('round(AVG((stats::json->>\'0-10\')::NUMERIC / sp.area)::NUMERIC, 4) AS "0-10"'),
            DB::raw('round(AVG((stats::json->>\'10-20\')::NUMERIC / sp.area)::NUMERIC, 4) AS "10-20"'),
            DB::raw('round(AVG((stats::json->>\'20-30\')::NUMERIC / sp.area)::NUMERIC, 4) AS "20-30"'),
            DB::raw('round(AVG((stats::json->>\'30-40\')::NUMERIC / sp.area)::NUMERIC, 4) AS "30-40"'),
            DB::raw('round(AVG((stats::json->>\'40-50\')::NUMERIC / sp.area)::NUMERIC, 4) AS "40-50"'),
            DB::raw('round(AVG((stats::json->>\'50-60\')::NUMERIC / sp.area)::NUMERIC, 4) AS "50-60"'),
            DB::raw('round(AVG((stats::json->>\'60-70\')::NUMERIC / sp.area)::NUMERIC, 4) AS "60-70"'),
            DB::raw('round(AVG((stats::json->>\'70-80\')::NUMERIC / sp.area)::NUMERIC, 4) AS "70-80"'),
            DB::raw('round(AVG((stats::json->>\'80-90\')::NUMERIC / sp.area)::NUMERIC, 4) AS "80-90"'),
            DB::raw('round(AVG((stats::json->>\'90-100\')::NUMERIC / sp.area)::NUMERIC, 4) AS "90-100"'),
            DB::raw('round(AVG((stats::json->>\'100-110\')::NUMERIC / sp.area)::NUMERIC, 4) AS "100-110"'),

            DB::raw('round(stddev((stats::json->>\'0-10\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_0-10"'),
            DB::raw('round(stddev((stats::json->>\'10-20\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_10-20"'),
            DB::raw('round(stddev((stats::json->>\'20-30\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_20-30"'),
            DB::raw('round(stddev((stats::json->>\'30-40\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_30-40"'),
            DB::raw('round(stddev((stats::json->>\'40-50\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_40-50"'),
            DB::raw('round(stddev((stats::json->>\'50-60\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_50-60"'),
            DB::raw('round(stddev((stats::json->>\'60-70\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_60-70"'),
            DB::raw('round(stddev((stats::json->>\'70-80\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_70-80"'),
            DB::raw('round(stddev((stats::json->>\'80-90\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_80-90"'),
            DB::raw('round(stddev((stats::json->>\'90-100\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_90-100"'),
            DB::raw('round(stddev((stats::json->>\'100-110\')::NUMERIC / sp.area)::NUMERIC, 4) AS "stddev_100-110"'),
        ])->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_layers_plots.satellite_type', 'sentinel')
            ->whereNotNull('stats')
            ->where(function ($query) {
                $query->whereNull('clouds_percent')
                    ->orWhere(DB::raw('round(clouds_percent::numeric, 2)'), 0);
            })
            ->where('so.status', 'processed')
            ->groupBy('crop_id', 'hybrid_id', 'su_satellite_layers_plots.date')
            ->restOrderBy($sort);

        if ($cropIds) {
            $plotsDataQuery->whereIn('crop_id', $cropIds);
        }
        if ($cropHybridId) {
            $plotsDataQuery->where('hybrid_id', $cropHybridId);
        }
        if (count($farmYears) > 1) {
            $plotsDataQuery->orWhere(function ($query) use ($farmYears) {
                foreach ($farmYears as $farmYear) {
                    $query->whereBetween('su_satellite_layers_plots.date', $farmYear);
                }
            });
        }
        if (1 === count($farmYears)) {
            $plotsDataQuery->whereBetween('su_satellite_layers_plots.date', current($farmYears));
        }

        $plotsData = $plotsDataQuery->get()->groupBy('crop_id', 'hybrid_id');

        $plotsData->transform(function ($item) {
            return $item->keyBy('date')->toArray();
        });

        return $plotsData->toArray();
    }

    /**
     * @throws GuzzleException
     *
     * @return array
     */
    public function getFilteredPlotsSoilsList(string $lang, array $filters, array $sort, int $limit, int $page)
    {
        if (count($filters['status']) || count($filters['package_type'])) {
            $response = $this->plotHttpService->getFilteredPlotsSoilsData([
                'status' => json_encode($filters['status']),
                'package_type' => json_encode($filters['package_type']),
                'customer_identification' => json_encode(Auth::user()->lastChosenOrganization),
            ]);
            $filters['order_uuids'] = $response['orderUUids'];
        }

        $plots = Plot::getFilteredPlotsSoilsList($lang, $filters, $sort)
            ->paginate($limit, ['gid'], 'page', $page);

        $orderUuids = array_unique(array_map(function ($item) {
            return $item->order_uuid;
        }, $plots->items()));

        $results = $this->plotHttpService->getPlotSoilsListData($orderUuids);

        $collection = collect($plots->items());
        $items = $collection->toArray();

        if (count($results)) {
            $items = array_map(function ($item) use ($results) {
                // assign virtual additional properties
                $item['contract'] = null;
                $item['packages'] = [];
                $item['recommendation_id'] = null;

                $key = array_search($item['uuid'], array_column($results, 'plot_uuid'));

                if (false !== $key) {
                    $item['contract'] = $results[$key]['contract'];
                    $item['packages'] = $results[$key]['packages'];
                    $item['recommendation_id'] = $results[$key]['recommendation_id'];
                }

                return $item;
            }, $items);
        }

        return [
            'rows' => $items,
            'total' => $plots->total(),
        ];
    }

    public function getPlotsDate(?int $organizationId = null, ?int $farmYear = null)
    {
        return Plot::getPlotsDate($organizationId, $farmYear);
    }

    /**
     * @param ?string $plotName
     * @param ?int $farmId
     * @param ?int $plotId
     *
     * @throws Exception
     */
    public function saveWorkMap(?string $plotName, ?int $farmId, ?int $plotId, array $removeGids = [], array $addGeoms = [])
    {
        if (count($removeGids)) {
            $plots = Plot::whereIn('gid', $removeGids)->where('is_editable', false)->get()->toArray();

            if (count($plots) > 0) {
                throw new LogicException('One of removable plots cannot be edited.');
            }
        }

        if (!isset($farmId) && count($removeGids)) {
            // Remove old plots
            Plot::whereIn('gid', $removeGids)->delete();

            return;
        }

        $farm = Farm::findOrFail($farmId);

        if (!isset($plotId) && count($addGeoms)) {
            // Create new plot(s)
            $this->createNewPlots($farm, $plotName, $addGeoms);

            return;
        }

        if (count($addGeoms) > 1 || count($removeGids) > 1) {
            // Merge or Split Polygons
            $this->createNewPlots($farm, $plotName, $addGeoms);

            if (count($removeGids)) {
                // Remove old plots
                Plot::whereIn('gid', $removeGids)->delete();
            }

            return;
        }

        if (!isset($plotId)) {
            return;
        }

        if (!count($addGeoms) && !count($removeGids)) {
            // Update existing plot without touching the geom field
            $this->updateExistingPlot($plotId, $plotName, $farm, $addGeoms);

            return;
        }

        if (1 == count($addGeoms)) {
            // Update existing plot and geom field
            $this->updateExistingPlot($plotId, $plotName, $farm, $addGeoms);

            return;
        }

        if (count($removeGids)) {
            // Remove old plots
            Plot::whereIn('gid', $removeGids)->delete();

            return;
        }
    }

    private function plotsForApprove($plotsCms, $withGeom = true)
    {
        $plotUuids = array_column($plotsCms, 'plotUuid');

        $query = Plot::getPlotsByUser()->select('gid', 'uuid as plotUuid');
        if ($withGeom) {
            $query->addSelect(DB::raw('st_centroid(st_transform(geom, 4326)) AS geom'));
        }
        $userPlots = $query->whereIn('su_satellite_plots.uuid', $plotUuids)->get()->toArray();

        $arrResult = [];
        foreach ($userPlots as $plotUser) {
            $foundPlot = array_filter($plotsCms, function ($plotCms) use ($plotUser) {
                return $plotCms['plotUuid'] == $plotUser['plotUuid'];
            });

            if (!$foundPlot) {
                continue;
            }

            $foundPlot = reset($foundPlot);
            $foundPlot['gid'] = $plotUser['gid'];
            if ($withGeom) {
                $foundPlot['geom'] = $plotUser['geom'];
            }
            $arrResult[] = $foundPlot;
        }

        return $arrResult;
    }

    private function updatePlotHelper(int $plotId, ?string $plotName, ?Farm $farm, ?array $geoms, array $crops = [], bool $irrigated = null, int $farmYear = null): void
    {
        $plot = Plot::findorFail($plotId);

        if (count($geoms) && !$plot->is_editable) {
            throw new LogicException('The plot cannot be edited.');
        }

        if (isset($plotName)) {
            $plot->name = $plotName;
        }

        if (isset($farm) && $farm->id !== $plot->farm_id) {
            $plot->farm()->associate($farm);

            UpdateFieldFarmIdInCMS::dispatch(
                $plot->uuid,
                $farm->id
            );
        }

        if (isset($geoms) && count($geoms) > 0) {
            $geom = current($geoms);
            $plot->area = DB::raw("round((ST_Area(ST_GeomFromText('{$geom}', " . Config::get('globals.DEFAULT_DB_CRS') . '))/1000)::numeric, 3)');
            $plot->geom = DB::raw("ST_GeomFromText('{$geom}', " . Config::get('globals.DEFAULT_DB_CRS') . ')');
        }

        $plot->save();

        if (isset($crops) && count($crops) > 0) {
            foreach ($crops as $crop) {
                if (isset($crop['should_be_deleted']) && true === $crop['should_be_deleted']) {
                    PlotCrop::find($crop['plot_crop_rel_id'])->delete();

                    continue;
                }

                if (date('Y', strtotime($crop['sowing_date'])) > date('Y', strtotime($crop['harvest_date']))) {
                    throw new Exception('Harvest date must be greater than sowing date.');
                }

                // Validate Crop category
                $cropCategory = null;
                if (isset($crop['category_id'])) {
                    $cropCategory = CropCategory::validateCropCategory($crop['id'], $crop['category_id']);
                }

                // Validate Crop hybrid
                $cropHybrid = null;
                if (isset($crop['hybrid_id'])) {
                    $cropHybrid = CropHybrid::validateCropHybrid($crop['id'], $crop['hybrid_id']);
                }

                $farmingYears = isset($farmYear)
                    ? [FarmingYear::get($farmYear)]
                    : $this->findFarmingYears($plot, $crop['sowing_date'], $crop['harvest_date']);

                foreach ($farmingYears as $farmingYear) {
                    PlotCrop::updateOrCreate(
                        [
                            'id' => $crop['plot_crop_rel_id'] ?? null,
                        ],
                        [
                            'is_primary' => isset($crop['is_primary']) && true === $crop['is_primary'],
                            'plot_id' => $plotId,
                            'irrigated' => $irrigated ?? false,
                            'category_id' => isset($cropCategory) ? $cropCategory->id : null,
                            'hybrid_id' => isset($cropHybrid) ? $cropHybrid->id : null,
                            'crop_id' => $crop['id'],
                            'from_date' => $crop['sowing_date'],
                            'to_date' => $crop['harvest_date'],
                            'year' => (int)$farmingYear['id'],
                        ]
                    );
                }
            }
        }

        // Set irrigated field in su_satellite_plots_crops
        $param['gid'] = $plotId;
        $this->cropService->setIrrigatedFromPlatform($param);
    }

    /**
     * Get Longitude And Latitude From gid.
     *
     * @param int $gid
     *
     * @return array
     */
    private function longitudeAndLatitude($gid)
    {
        $query = Plot::select([
            DB::raw('ST_X(ST_PointOnSurface(geom)) AS longitude'),
            DB::raw('ST_Y(ST_PointOnSurface(geom)) AS latitude'),
        ])
            ->where('su_satellite_plots.gid', $gid)
            ->first();

        if (is_null($query)) {
            return [];
        }

        return $query->toArray();
    }

    private function getHistoryCollection(
        int $plotId,
        string $type,
        int $organizationId,
        string $sort,
        string $sortDirection,
        ?string $fromDate,
        ?string $toDate,
        ?string $element,
        ?int $maxCloudPercentage = 100
    ) {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = LayerPlot::getDataQuery()->select([
            'su_satellite_layers_plots.id',
            'su_satellite_layers_plots.plot_id',
            'su_satellite_layers_plots.layer_name',
            'su_satellite_layers_plots.date',
            'su_satellite_layers_plots.date_time',
            'su_satellite_layers_plots.stats',
            'su_satellite_layers_plots.type AS order_type',
            'su_satellite_layers_plots.mean',
            'su_satellite_layers_plots.satellite_type',
            'su_satellite_layers_plots.is_viewed',
            DB::raw('ROUND(su_satellite_layers_plots.clouds_percent::NUMERIC, 1) AS clouds_percent'),
            'sopst.sampling_type_id',
            'sopst.sampling_type_name',
            'su_satellite_layers_plots.has_water_pounds',
            'su_satellite_layers_plots.order_id',
            DB::raw("round((sp.area * {$areaCoef})::numeric, 3) as area"),
            'spc.crop_id',
            'slpf.web_path',
        ])
            ->join('su_satellite_layers_plots_files AS slpf', 'slpf.layer_plot_id', '=', 'su_satellite_layers_plots.id')
            ->leftJoin('su_satellite_orders_plots_sampling_types as sopst', 'sopst.id', '=', 'su_satellite_layers_plots.sopst_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', $organizationId)
            ->where('uf.is_visible', true)
            ->where('su_satellite_layers_plots.plot_id', $plotId)
            ->where('slpf.type', 'PNG')
            ->orderBy($sort, $sortDirection);

        if ('soil' !== $type) {
            $query->where('so.status', 'processed');
        }

        if ('index' == $type) {
            $allowedSatelliteTypes = User::allowedSatelliteTypes();
            $query->whereIn('su_satellite_layers_plots.satellite_type', $allowedSatelliteTypes);
            $query->whereIn('su_satellite_layers_plots.type', ['index', 'index_water']);
        } else {
            $query->where('su_satellite_layers_plots.type', $type);
        }

        if ($fromDate) {
            $query->where(DB::raw('su_satellite_layers_plots.date::DATE'), '>=', $fromDate);
        }

        if ($toDate) {
            $query->where(DB::raw('su_satellite_layers_plots.date::DATE'), '<=', $toDate);
        }

        if ($element && strlen($element) > 0) {
            $query->where(('su_satellite_layers_plots.element'), $element);
        }

        if (isset($maxCloudPercentage)) {
            $query->where(DB::raw('COALESCE(su_satellite_layers_plots.clouds_percent, 0)'), '<=', $maxCloudPercentage);
        }

        if ('soil' === $type) {
            $query->addSelect([
                DB::raw("split_part(su_satellite_layers_plots.layer_name, '_', 4) AS compound"),
                DB::raw('su_satellite_layers_plots.stats_type AS type'),
                DB::raw('su_satellite_layers_plots.element AS soil_element'),
            ]);

            $query->where('su_satellite_layers_plots.stats_type', '=', 'summarized');
            $query->where('so.status', '!=', 'canceled');

            $historyCollection = $query->get();

            $historyCollection->each(function ($row) {
                $row->stats_as_chart_data = $row->stats_in_soil_chart_format;
            });

            return $historyCollection;
        }

        return $query->get();
    }

    private function addChartData($historyCollection, ?string $fromDate, ?string $toDate)
    {
        $cropIds = collect($historyCollection->pluck('crop_id'))->unique()->values();
        $farmingYearDates = [[$fromDate, $toDate]];
        $plotsAvgData = $this->getPlotsAvgData($cropIds, $farmingYearDates);

        $historyCollection->each(function ($row) use ($plotsAvgData) {
            if (!isset($plotsAvgData[$row->crop_id]) || !isset($plotsAvgData[$row->crop_id][$row->date])) {
                $row->stats_as_chart_data = $row->stats_in_chart_format;

                return;
            }

            // Add Average Stats for Index Charts
            $arrContentChartData = $row->stats_in_chart_format;
            $arrHeader = $arrContentChartData[0];
            unset($arrContentChartData[0]);

            $arrToAdd = ['Average', ['role' => 'tooltip']];
            $arrHeaderMerged = array_merge($arrHeader, $arrToAdd);

            $arrAvgData = $plotsAvgData[$row->crop_id][$row->date];
            $plotArea = $row->area;

            $arrContentChartData = array_map(function ($row) use ($arrAvgData, $plotArea) {
                $indexClass = $row[0];
                $value = round($arrAvgData[$indexClass] * $plotArea, 3);
                $textAvg = trans('general.distribution_curve');
                $arrToAdd = [$value, $textAvg];

                return array_merge($row, $arrToAdd);
            }, $arrContentChartData);

            array_unshift($arrContentChartData, $arrHeaderMerged);

            $row->stats_as_chart_data = $arrContentChartData;
        });

        return $historyCollection;
    }

    /**
     * @return array
     */
    private function findFarmingYears(Plot $plot, string $sowingDate, string $harvestDate)
    {
        $farmingYears = FarmingYear::getFarmingYearsByRange($sowingDate, $harvestDate);

        $results = Plot::join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', '=', 'su_satellite_plots.uuid')
            ->select(['su_satellite_plots.gid', 'so.year'])
            ->join('su_satellite_orders AS so', 'so.uuid', '=', 'sopr.order_uuid')
            ->where('su_satellite_plots.gid', $plot->gid)
            ->where('so.type', 'index')
            ->whereIn('so.year', array_column($farmingYears, 'year'))
            ->get()->toArray();

        $results = array_column($results, 'year');
        $farmingYears = array_values(
            array_filter($farmingYears, function ($element) use ($results) {
                return in_array($element['year'], $results);
            })
        );

        foreach ($plot->crops()->getResults()->toArray() as $plotCrop) {
            $farmingYearIndex = in_array($plotCrop['pivot']['year'], array_column($farmingYears, 'year'));
            if (false !== $farmingYearIndex) {
                if ($this->hasOverlapDates($plot->gid, $plotCrop['id'], $sowingDate, $harvestDate)) {
                    unset($farmingYears[$farmingYearIndex]);
                }
            }
        }

        return array_values($farmingYears);
    }

    private function hasOverlapDates(int $gid, int $cropId, string $sowingDate, string $harvestDate): bool
    {
        return PlotCrop::selectRaw("daterange(from_date, to_date, '[]') && daterange('" . $sowingDate . "', '" . $harvestDate . "', '[]')")
            ->where('plot_id', $gid)
            ->where('crop_id', $cropId)
            ->get()
            ->count() > 0;
    }
}
