<?php

namespace App\Services\Order;

use App\Classes\CMS\AnalysisService;
use App\Classes\CMS\ContractService;
use App\Classes\PlotShape;
use App\Helpers\Helper;
use App\Models\API\OrdersTypeMapping;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\Organization;
use App\Models\Plot;
use App\Models\SoilGrid;
use App\Models\SoilGridParams;
use App\Models\SoilPoints;
use App\Models\StaticModels\FarmingYear;
use App\Services\FarmingYear\FarmingYearService;
use App\Services\Plot\PlotService;
use Auth;
use Config;
use DB;
use Exception;
use hanneskod\classtools\Exception\LogicException;
use Illuminate\Support\Collection;
use Webpatser\Uuid\Uuid;

class OrderService
{
    private $plotShape;
    private $contractService;
    private $analysisService;
    /**
     * @var FarmingYearService
     */
    private $farmingYearService;

    public function __construct(ContractService $contractService, PlotShape $plotShape, AnalysisService $analysisService, FarmingYearService $farmingYearService)
    {
        $this->plotShape = $plotShape;
        $this->contractService = $contractService;
        $this->analysisService = $analysisService;
        $this->farmingYearService = $farmingYearService;
    }

    public function postStore(int $contractId, string $contractType, array $plots, string $organizationId, array $headerParams, ?int $existingOrderId)
    {
        $returnCmsPackageFieldData = [];
        $returnCmsGridPointData = [];
        $protocolData = null;

        $orderTypeMapping = OrdersTypeMapping::all()->toArray();
        $packages = $this->contractService->getAllServiceOrSubscriptionPackagesByContractId($contractId, $headerParams);

        DB::beginTransaction();

        try {
            foreach ($packages as $key => $package) {
                foreach ($orderTypeMapping as $orderType) {
                    if ($orderType['cms_type'] === $package['slug']) {
                        $package['orderType'] = $orderType['order_type'];

                        break;
                    }
                }

                $orderData = $this->createOrder($organizationId, $package['orderType'], $plots, $package['startDate'], $package['endDate']);

                $returnCmsPackageFieldData[$key]['package_id'] = $package['id'];
                $returnCmsPackageFieldData[$key]['params'] = $this->formatPackageFieldData($plots, $orderData['orderUuid']);

                if ('soil' == $package['orderType']) {
                    $returnCmsGridPointData[$key]['package_id'] = $package['id'];
                    $returnCmsGridPointData[$key]['params'] = $this->generateGridData($plots, $orderData['orderId'], $orderData['orderUuid'], $existingOrderId, $package['isFullSampling']);
                }
            }

            // return data to CMS
            if (count($returnCmsPackageFieldData) > 0) {
                $protocolData = $this->contractService->savePackageFields($contractType, $returnCmsPackageFieldData);
            }
            $this->analysisService->savePackageGridPoints($contractType, $returnCmsGridPointData);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw new Exception($e->getMessage());
        }

        return $protocolData;
    }

    /**
     * @deprecated Used for angular 1 project (admin)
     */
    public function getPlots(Order $order, $sort, $areaCoef)
    {
        switch ($order->type) {
            case 'vra':
                $orderPlots = Plot::getPlotsOrderVra($order, 'su_orders_satellite_vra', $sort, $areaCoef);
                $orderPlots = $this->setNewParamsVra($orderPlots);

                break;
            case 'soil_vra':
                $orderPlots = Plot::getPlotsOrderVra($order, 'su_orders_soil_vra', $sort, $areaCoef);
                $orderPlots = $this->setNewParamsVra($orderPlots);

                break;
            default:
                $orderPlots = Plot::getPlotsOrder($order, $sort, $areaCoef);
                $orderPlots = $this->setNewParams($orderPlots);
        }

        return $orderPlots;
    }

    /**
     * @throws Exception
     *
     * @return array
     */
    public function createOrder(string $organizationId, string $orderType = null, array $plots = [], string $startDate, string $endDate)
    {
        $user = Auth::user();

        $pricePerDka = 0;
        if (isset(Config::get('globals.PRICES_PER_DKA')[$orderType])) {
            $pricePerDka = Config::get('globals.PRICES_PER_DKA')[$orderType];
        }

        $plotsId = array_column($plots, 'id');
        $organizationName = Organization::select('name')->where('id', $organizationId)->first()->name;

        $order = new Order();
        $order->organization()->associate($organizationId);
        $order->type = $orderType;
        $order->year = FarmingYear::getYearbyDate($startDate);
        $order->from_date = $startDate;
        $order->to_date = $endDate;
        $order->date = DB::raw('now()');
        $order->status = 'soil' === $orderType ? 'waiting_payment' : 'paid';
        $order->color = Helper::getRandomHexColor();
        $order->createdBy()->associate($user);
        $order->uuid = Uuid::generate(4)->string;
        $order->company_name = $organizationName;

        $calculateResult = PlotService::calculatePlotsArea($plotsId, $pricePerDka);
        $plots = $calculateResult['plots'];
        $wholeArea = $calculateResult['wholeArea'];
        $wholePrice = $calculateResult['wholePrice'];

        // add whole price and area for a order
        $order->price = $wholePrice;
        $order->area = $wholeArea;

        $availableDemoArea = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float)$user->ordered_area, 0);
        if ($user->is_trial && $wholeArea <= $availableDemoArea && 'index' === $orderType) {
            $order->status = 'paid';
            $order->end_price = 0;

            $user->ordered_area += $wholeArea;
            $user->save();

            $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
            $availableDemoArea = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float)$user->ordered_area, 0) * $areaCoef;
        }

        $order->save();

        Plot::whereIn('gid', $plotsId)->update(['is_editable' => false]);

        $pivotRows = [];

        foreach ($plots as $plotId => $plotData) {
            $pivotRows[] = [
                'order_id' => $order->id,
                'order_uuid' => $order->uuid,
                'plot_id' => $plotId,
                'plot_uuid' => $plotData['uuid'],
                'price' => $plotData['price'],
            ];
        }

        OrderPlotRel::insert($pivotRows);

        return ['availableDemoArea' => $availableDemoArea, 'orderId' => $order->id, 'orderUuid' => $order->uuid];
    }

    public function cancelOrder($orderId): bool
    {
        Order::cancelOrder($orderId, Auth::user()->id);

        return true;
    }

    public function generateGridData(array $plots, int $orderId, string $orderUuid, ?int $existingOrderId = null, ?bool $isFullSamplingPackage = null): array
    {
        $soilGridData = [];
        $soilGridParamsData = [];
        $soilPointsData = [];
        $returnData = [];
        $deleteSoilData = [];
        $existingSoilGridParams = [];

        if (!$isFullSamplingPackage) {
            $existingSoilGridParams = $this->getGridTypeAndCellAreaForControlPeriod($plots);
        }

        foreach ($plots as &$plot) {
            $gridType = $plot['gridType'] ?? null;

            if (SoilGridParams::USE_EXISTING === $gridType && $existingOrderId) {
                $this->saveExistingGridData($plot, $existingOrderId, $orderId, $orderUuid, $soilGridData, $soilGridParamsData, $soilPointsData, $returnData, $deleteSoilData);

                continue;
            }

            if (SoilGridParams::CUSTOM_GRID === $gridType) {
                $this->saveCustomGridData($plot, $gridType, $orderId, $orderUuid, $soilGridData, $soilGridParamsData, $soilPointsData, $returnData);

                continue;
            }

            if (!$isFullSamplingPackage) {
                $gridType = $existingSoilGridParams[$plot['overlaps_with']['plot_uuid']]['type'];
                $plot['customCellArea'] = $existingSoilGridParams[$plot['overlaps_with']['plot_uuid']]['custom_cell_area'];
            }

            if (is_null($gridType)) {
                throw new LogicException('gridType is missing');
            }

            $this->saveGridsData($plot, $gridType, $orderUuid, $soilGridData, $soilGridParamsData, $soilPointsData, $returnData, $deleteSoilData, $isFullSamplingPackage);
        }

        if (count($deleteSoilData)) {
            SoilGrid::getBySoprId($deleteSoilData)->delete();
            SoilPoints::getBySoprId($deleteSoilData)->delete();
        }

        SoilGridParams::insert($soilGridParamsData);
        SoilGrid::insert($soilGridData);
        SoilPoints::insert($soilPointsData);

        return $returnData;
    }

    public function getFarmYearsWithOrders(int $plotId): Collection
    {
        $plotFarmingYearsWithOrders = Order::getOrdersQuery()
            ->where('su_satellite_orders.status', '!=', 'canceled')
            ->whereIn('su_satellite_orders.type', ['index', 'soil'])
            ->where('su_satellite_orders_plots_rel.plot_id', $plotId)
            ->distinct()->pluck('year')->toArray();

        $allFarmingYears = $this->farmingYearService->getFarmingYearsByOrganization(Auth::user()->lastChosenOrganization);
        $farmingYears = $allFarmingYears->filter(function ($value) use ($plotFarmingYearsWithOrders) {
            return in_array($value['year'], $plotFarmingYearsWithOrders);
        })->values();

        return $farmingYears;
    }

    private function saveGridsData(
        array $plot,
        string $gridType,
        string $orderUuid,
        array &$soilGridData,
        array &$soilGridParamsData,
        array &$soilPointsData,
        array &$returnData,
        array &$deleteSoilData,
        ?bool $isFullSamplingPackage
    ) {
        if (!$isFullSamplingPackage) {
            $data = $this->plotShape->getDataWithOldGridPoints(
                $plot['plotUuid'],
                $orderUuid,
                $plot['overlaps_with']['plot_uuid'],
                $plot['overlaps_with']['order_uuid'],
                $gridType,
                $plot['customCellArea'] ?? null
            );
        } else {
            $data = $this->plotShape->getDataWithNewGridPoints(
                $plot['plotUuid'],
                $orderUuid,
                $gridType,
                $plot['customCellArea'] ?? null
            );
        }

        foreach ($data as $gridData) {
            $soilGridData[] = [
                'sopr_id' => $gridData->sopr_id,
                'sample_id' => $gridData->sample_id,
                'color' => $gridData->color,
                'geom' => $gridData->cell,
            ];

            $soilPointsData[] = [
                'sopr_id' => $gridData->sopr_id,
                'sample_id' => $gridData->sample_id,
                'geom' => $gridData->point,
            ];

            $soilGridParamsData[] = [
                'type' => $gridType,
                'area' => $plot['area'],
                'datetime' => date('Y-m-d H:i:s'),
                'order_id' => $gridData->order_id,
                'user_id' => Auth::user()->id,
                'status' => 'processed',
                'plot_id' => $plot['id'],
                'custom_cell_area' => $plot['customCellArea'] ?? null,
            ];

            $returnData[] = [
                'plot_uuid' => $plot['plotUuid'],
                'area' => $plot['area'],
                'order_uuid' => $orderUuid,
                'sopr_id' => $gridData->sopr_id,
                'grid_type' => $gridType,
            ];

            // Delete old data
            $deleteSoilData[] = $gridData->sopr_id;
        }
    }

    private function saveExistingGridData(
        array $plot,
        int $existingOrderId,
        int $orderId,
        string $orderUuid,
        array &$soilGridData,
        array &$soilGridParamsData,
        array &$soilPointsData,
        array &$returnData,
        array &$deleteSoilData
    ) {
        $soprOld = OrderPlotRel::where('plot_id', $plot['id'])->where('order_id', $existingOrderId)->first();
        $soprNew = OrderPlotRel::where('plot_id', $plot['id'])->where('order_id', $orderId)->first();

        $soilGridParamsDataOld = SoilGridParams::where('order_id', $existingOrderId)->where('plot_id', $plot['id'])->get()->toArray();
        foreach ($soilGridParamsDataOld as $data) {
            $soilGridParamsData[] = [
                'type' => $data['type'],
                'area' => $plot['area'],
                'datetime' => date('Y-m-d H:i:s'),
                'order_id' => $orderId,
                'user_id' => Auth::user()->id,
                'status' => 'processed',
                'plot_id' => $plot['id'],
                'custom_cell_area' => $data['custom_cell_area'],
            ];

            $returnData[] = [
                'plot_uuid' => $plot['plotUuid'],
                'area' => $plot['area'],
                'order_uuid' => $orderUuid,
                'sopr_id' => $soprNew->id,
                'grid_type' => $data['type'],
            ];
        }

        $soilGridDataOld = SoilGrid::where('sopr_id', $soprOld->id)->get()->toArray();
        foreach ($soilGridDataOld as $data) {
            $soilGridData[] = [
                'sopr_id' => $soprNew->id,
                'sample_id' => $data['sample_id'],
                'color' => $data['color'],
                'geom' => $data['geom'],
            ];
        }

        $soilPointsDataOld = SoilPoints::where('sopr_id', $soprOld->id)->get()->toArray();
        foreach ($soilPointsDataOld as $data) {
            $soilPointsData[] = [
                'sopr_id' => $soprNew->id,
                'sample_id' => $data['sample_id'],
                'geom' => $data['geom'],
            ];
        }

        // Delete old data
        $deleteSoilData[] = $soprNew->id;
    }

    private function saveCustomGridData(
        $plot,
        $gridType,
        $orderId,
        $orderUuid,
        array &$soilGridData,
        array &$soilGridParamsData,
        array &$soilPointsData,
        array &$returnData
    ) {
        $sopr = OrderPlotRel::where([['plot_id', $plot['id']], ['order_id', $orderId]])->first();
        $tocrs = Config::get('globals.DEFAULT_DB_CRS');

        $gridData = collect($plot['gridGeoJson']['features']);
        $gridProperties = $gridData->pluck('properties');
        $plot['customCellArea'] = $gridProperties->min('cell_area');

        foreach ($plot['gridGeoJson']['features'] as $gridData) {
            foreach ($gridData['geometry']['geometries'] as $geometries) {
                $geom = json_encode($geometries);
                if ('Polygon' === $geometries['type']) {
                    $soilGridData[] = [
                        'sopr_id' => $sopr->id,
                        'sample_id' => $gridData['properties']['sample_id'],
                        'color' => $gridData['properties']['color'],
                        'geom' => DB::raw("st_transform(st_setsrid(ST_GeomFromGeoJSON('{$geom}'), 3857), {$tocrs})"),
                    ];

                    continue;
                }

                $soilPointsData[] = [
                    'sopr_id' => $sopr->id,
                    'sample_id' => $gridData['properties']['sample_id'],
                    'geom' => DB::raw("st_transform(st_setsrid(ST_GeomFromGeoJSON('{$geom}'), 3857), {$tocrs})"),
                ];
            }

            $soilGridParamsData[] = [
                'type' => $gridType,
                'area' => $plot['area'],
                'datetime' => date('Y-m-d H:i:s'),
                'order_id' => $orderId,
                'user_id' => Auth::user()->id,
                'status' => 'processed',
                'plot_id' => $plot['id'],
                'custom_cell_area' => $plot['customCellArea'],
            ];

            $returnData[] = [
                'plot_uuid' => $plot['plotUuid'],
                'area' => $plot['area'],
                'order_uuid' => $orderUuid,
                'sopr_id' => $sopr->id,
                'grid_type' => $gridType,
            ];
        }
    }

    /**
     * @deprecated Used for angular 1 project (admin)
     */
    private function setNewParamsVra($orderPlots)
    {
        $orderPlots->each(function ($plot) {
            $plot['extent'] = Helper::parseExtentToOpenLayer($plot['extent']);
        });

        return $orderPlots;
    }

    /**
     * @deprecated Used for angular 1 project (admin)
     */
    private function setNewParams($orderPlots)
    {
        $orderPlots->each(function ($plot) {
            $plot['extent'] = Helper::parseExtentToOpenLayer($plot['extent']);
            $plot['sampling_types'] = json_decode($plot['sampling_types']);
            $plot['all_sampling_types'] = $this->analysisService->getSamplingTypes();
            $plot['cell_ids'] = '{NULL}' == $plot['cell_ids'] ? [] : explode(',', trim($plot['cell_ids'], '{}'));

            if (is_null($plot['leaf_sample_cells'])) {
                $plot['leaf_sample_cells'] = [];
            } else {
                $plot['leaf_sample_cells'] = explode(',', $plot['leaf_sample_cells']);
                if ('' == $plot['leaf_sample_cells'][0]) {
                    $plot['leaf_sample_cells'] = [];
                }
            }
        });

        return $orderPlots;
    }

    private function formatPackageFieldData(array $plots, string $orderUuid)
    {
        $returnData = [];
        foreach ($plots as $plot) {
            $returnData[] = [
                'plot_uuid' => $plot['plotUuid'],
                'order_uuid' => $orderUuid,
                'area' => $plot['area'],
                'farm_id' => $plot['farmId'],
                'parent' => $plot['overlaps_with'] ?? null, // plotUuid and orderUuid of the already existing plot with same geometry in the full sampling package
            ];
        }

        return $returnData;
    }

    private function getGridTypeAndCellAreaForControlPeriod($plots): Collection
    {
        $plotsCollection = collect($plots)->pluck('overlaps_with');
        $plots = $plotsCollection->pluck('plot_uuid');
        $orders = $plotsCollection->pluck('order_uuid');

        $soilGridParams = SoilGridParams::selectRaw(
            '
            distinct ssopr.plot_uuid,
            su_satellite_soil_grid_params.type,
            su_satellite_soil_grid_params.custom_cell_area'
        )
            ->join('su_satellite_orders as sso', 'sso.id', 'su_satellite_soil_grid_params.order_id')
            ->join('su_satellite_orders_plots_rel as ssopr', function ($join) {
                $join->on('sso.uuid', '=', 'ssopr.order_uuid')
                    ->on(DB::raw('(su_satellite_soil_grid_params.plot_id)::int'), '=', 'ssopr.plot_id');
            })
            ->whereIn('sso.uuid', $orders)
            ->whereIn('ssopr.plot_uuid', $plots)
            ->get()->toArray();

        return collect($soilGridParams)->keyBy('plot_uuid');
    }
}
