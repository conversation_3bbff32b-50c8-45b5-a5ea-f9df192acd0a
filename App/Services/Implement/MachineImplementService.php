<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 9/15/2020
 * Time: 4:41 PM.
 */

namespace App\Services\Implement;

use App\Classes\Wialon\WialonErrorCodes;
use App\Exceptions\NotFoundException;
use App\Jobs\DeactivateIntegrationJob;
use App\Models\Integration;
use App\Models\IntegrationReportsTypes;
use App\Models\MachineEvent;
use App\Models\MachineImplement;
use App\Services\Wialon\WialonService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MachineImplementService
{
    private $wialonService;

    public function __construct(WialonService $wialonService)
    {
        $this->wialonService = $wialonService;
    }

    public function syncImplementsByIntegration(Integration $integration): array
    {
        if (Integration::ACTIVE !== $integration->status) {
            throw new NotFoundException('No active integrations found!');
        }

        $integrationReportType = $integration->reports()
            ->join('su_integration_reports_types AS sirt', 'sirt.id', '=', 'su_integrations_reports.integration_reports_types_id')
            ->where([
                ['sirt.name', IntegrationReportsTypes::IMPLEMENTS_SYNC],
                ['sirt.execution', IntegrationReportsTypes::ON_REQUEST],
            ])
            ->select(
                'sirt.url',
                'sirt.params'
            )
            ->first();

        if (!isset($integrationReportType)) {
            throw new Exception('Cannot find implements_sync report for this integration');
        }

        // Use 'wialon_unit_id' column as array index.
        $machineImplemetsFilter = [
            'integration_id' => $integration->id,
        ];
        $existingImplements = MachineImplement::getFilteredMachineImplementsQuery($machineImplemetsFilter)
            ->get()
            ->keyBy('wialon_unit_id')
            ->toArray();

        $newImplements = [];

        $jsonParams = $integrationReportType['params'];
        $svcType = 'core/search_items';
        $baseUrl = $integrationReportType['url'];

        try {
            $this->wialonService->login($integration->token, $baseUrl);
        } catch (Exception $e) {
            if (
                WialonErrorCodes::TOKEN_USER_NOT_FOUND === $e->getCode()
                || WialonErrorCodes::ACCESS_DENIED === $e->getCode()
            ) {
                dispatch(new DeactivateIntegrationJob(
                    $integration->getConnectionName(),
                    $integration,
                ));
            }

            throw $e;
        }

        $arrReport = $this->wialonService->makeRequest($svcType, $jsonParams);

        if (isset($arrReport['error']) || !isset($arrReport['items'])) {
            throw new Exception('error: ' . $arrReport['error'] . ', svc: ' . $svcType . ' , params: ' . $jsonParams . ', url: ' . $baseUrl);
        }

        $wialonImplements = $arrReport['items'][0]['trlrs'] ?? [];

        foreach ($wialonImplements as $implement) {
            $implementName = $implement['n'] ?? null;
            $wialonUnitId = $implement['id'] ?? null;

            if ($wialonUnitId && isset($existingImplements[$wialonUnitId])) {
                // The implement exists in the db.
                $existingImplements[$wialonUnitId]['existing'] = true;

                continue;
            }

            // The implement does not exist in the db.
            $implementWidth = floatval($implement['jp']['width'] ?? 0);
            $newImplements[] = [
                'id' => null,
                'organization_id' => null,
                'name' => $implementName,
                'width' => $implementWidth,
                'status' => MachineImplement::STATUS_ACTIVE,
                'integration_id' => null,
                'wialon_unit_id' => $wialonUnitId,
                'work_operations' => [],
                'existing' => false,
            ];
        }

        $allImplements = array_merge($existingImplements, $newImplements);

        return array_values($allImplements);
    }

    public function getImplementsByIntegrationUrlAndToken(string $url, string $token): array
    {
        $this->wialonService->login($token, $url);

        $flags = 65537; // Units flags: 1 (base flag) + 65536 (trailers)
        $wialonImplementsData = $this->wialonService->searchItems(WialonService::ITEMS_TYPE_RESOURCE, '*', '*', $flags);
        $wialonImplements = $wialonImplementsData['items'][0]['trlrs'] ?? []; // Get the trailers for the first resource
        $machineImplements = [];

        foreach ($wialonImplements as $implement) {
            $implementName = $implement['n'] ?? null;
            $wialonUnitId = $implement['id'] ?? null;
            $implementWidth = floatval($implement['jp']['width'] ?? 0);

            $machineImplements[] = [
                'id' => null,
                'organization_id' => null,
                'name' => $implementName,
                'width' => $implementWidth,
                'status' => MachineImplement::STATUS_ACTIVE,
                'integration_id' => null,
                'wialon_unit_id' => $wialonUnitId,
                'work_operations' => [],
                'existing' => false,
            ];
        }

        return $machineImplements;
    }

    /**
     * @return array
     */
    public function getWorkOperationsWithImplementsCnt(array $filter)
    {
        $organizationId = Auth::user()->lastChosenOrganization->id;

        return MachineEvent::getEventsCountByWorkOperationsQuery($organizationId, $filter)->get()->toArray();
    }

    /**
     * Get all statuses from machine_implement_status_enum.
     */
    public function getImplementStatuses(): array
    {
        return array_column(DB::select('SELECT UNNEST(ENUM_RANGE(NULL::machine_implement_status_enum)) AS status'), 'status');
    }

    public function updateImplement(MachineImplement $implement, array $requestData)
    {
        DB::beginTransaction();

        try {
            $implement->update($requestData);

            if (isset($requestData['work_operation_ids'])) {
                $implement->workOperations()->sync($requestData['work_operation_ids']);
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return MachineImplement::with(['workOperations' => function ($query) {
            return $query->select('su_work_operations.*');
        }])->where('id', $implement->id)->first();
    }
}
