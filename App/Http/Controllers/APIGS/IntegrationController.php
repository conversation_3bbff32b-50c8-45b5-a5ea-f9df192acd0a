<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Integration\StoreIntegrationRequest;
use App\Http\Requests\Integration\UpdateIntegrationRequest;
use App\Models\Integration;
use App\Services\Integration\IntegrationService;
use App\Services\RequestLog\RequestLogService;
use App\Services\Wialon\WialonService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Class IntegrationController.
 */
class IntegrationController extends BaseController
{
    private $requestLogService;
    private $wialonService;
    private $integrationService;

    public function __construct(
        RequestLogService $requestLogService,
        WialonService $wialonService,
        IntegrationService $integrationService
    ) {
        $this->requestLogService = $requestLogService;
        $this->wialonService = $wialonService;
        $this->integrationService = $integrationService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/integration/contract/:contractId",
     *     summary="Get integration",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @param string $organizationId
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function showByContract(int $contractId, Request $request)
    {
        $validator = Validator::make(array_merge([
            'contract_id' => $contractId,
            $request->all(),
        ]), [
            'contract_id' => 'required| integer',
            'package_slug_short' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $packageSlugShort = $request->get('package_slug_short');
        $integrations = $this->integrationService->getIntegrationsByContract($contractId, $packageSlugShort);

        return new JsonResponse($integrations, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/integration/history/:integration",
     *     summary="Get integration history",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function getIntegrationHistory(Integration $integration, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $log = $this->requestLogService->getIntegrationHistory($integration->id);

        return new JsonResponse(['rows' => $log], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/integration",
     *     summary="Create integration",
     *
     *     @OA\Response(
     *         response="200",
     *         description="Array"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function store(StoreIntegrationRequest $request)
    {
        $integrationData = [
            'token' => $request->get('token'),
            'organization_id' => $request->get('organization_id'),
            'contract_id' => $request->get('contract_id'),
            'package_id' => $request->get('package_id'),
            'package_slug_short' => $request->get('package_slug_short'),
            'package_period' => $request->get('package_period'),
            'integration_address' => $request->get('integration_address_id'),
            'status' => $request->get('status'),
        ];

        if (Integration::PACKAGE_SLUG_SHORT_FT === $integrationData['package_slug_short']) {
            $this->authorize('createFTIntegration', Integration::class);
            $machineUnits = $request->get('machine_units', []);
            $machineImplements = $request->get('machine_implements', []);

            $integration = $this->integrationService->createFTIntegration($integrationData, $machineUnits, $machineImplements);

            return new JsonResponse($integration, 201);
        }

        $this->authorize('createIMIntegration', Integration::class);
        $integration = $this->integrationService->storeIntegration($integrationData);

        return new JsonResponse($integration, 201);
    }

    /**
     * @OA\Put(
     *     path="/apigs/integration/:integrationId",
     *     summary="Update integration data",
     *
     *     @OA\Response(
     *         response="200"
     *     )
     * )
     *
     * @throws ValidationException
     *
     * @return JsonResponse
     */
    public function updateIntegration(Integration $integration, UpdateIntegrationRequest $request)
    {
        $integrationNewData = [
            'token' => $request->get('token', $integration->token),
            'integration_address' => (int) $request->get('integration_address_id', $integration->integrationAddress()->first()->id),
            'status' => $request->get('status', $integration->status),
        ];

        if (Integration::INACTIVE === $integrationNewData['status']) {
            $integration
                ->deactivate()
                ->save();

            return $integration;
        }

        if (Integration::PACKAGE_SLUG_SHORT_FT === $integration->package_slug_short) {
            $machineUnitsData = $request->get('machine_units', []);
            $machineImplementsData = $request->get('machine_implements', []);
            $updatedIntegration = $this->integrationService->updateFTIntegration($integration, $integrationNewData, $machineUnitsData, $machineImplementsData);

            return new JsonResponse($updatedIntegration, 200);
        }

        $updatedIntegration = $this->integrationService->updateIntegration($integration, $integrationNewData);

        return new JsonResponse($updatedIntegration, 200);
    }
}
