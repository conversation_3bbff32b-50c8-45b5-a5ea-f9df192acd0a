<?php

namespace App\Http\Requests\Integration;

use Illuminate\Foundation\Http\FormRequest;

class StoreIntegrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'token' => 'required|string',
            'organization_id' => 'required|numeric',
            'contract_id' => 'required|numeric',
            'package_id' => 'required|numeric',
            'package_slug_short' => 'required|string',
            'package_period' => 'required|string',
            'integration_address_id' => 'required|integer',
            'status' => 'required|string',
            'machine_units' => 'array',
            'machine_units.*.name' => 'required|string',
            'machine_units.*.type' => 'required|string',
            'machine_units.*.last_communication' => 'present|string|nullable',
            'machine_units.*.last_position_geojson' => 'present|nullable|array',
            'machine_units.*.wialon_unit_imei' => 'required|integer',
            'machine_units.*.wialon_unit_id' => 'present|integer|nullable',
            'machine_implements' => 'array',
            'machine_implements.*.wialon_unit_id' => 'present|integer|nullable',
            'machine_implements.*.name' => 'required|string',
            'machine_implements.*.width' => 'required|numeric',
            'machine_implements.*.work_operation_ids' => 'array',
            'machine_implements.*.work_operation_ids.*' => 'int',
            'machine_implements.*.status' => 'required|string',
        ];
    }
}
