<?php

namespace App\Providers\Integration;

use App\Services\Integration\IntegrationService;
use Illuminate\Support\ServiceProvider;

class IntegrationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot() {}

    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->singleton(IntegrationService::class, function () {
            $contractService = $this->app->make('App\Classes\CMS\ContractService');
            $wialonService = $this->app->make('App\Services\Wialon\WialonService');

            return new IntegrationService($contractService, $wialonService);
        });
    }
}
